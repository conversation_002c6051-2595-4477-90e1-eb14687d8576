#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
极端降水事件(EPE)数据掩膜处理脚本
根据研究区掩膜栅格对数据2-8进行裁剪处理
"""

import os
import glob
import rasterio
import numpy as np
from pathlib import Path
import logging
from tqdm import tqdm

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EPEDataMasker:
    def __init__(self, mask_path, output_base_path):
        """
        初始化数据掩膜处理器
        
        Args:
            mask_path: 研究区掩膜栅格路径
            output_base_path: 输出基础路径
        """
        self.mask_path = mask_path
        self.output_base_path = output_base_path
        self.mask_data = None
        self.mask_profile = None
        
        # 加载掩膜数据
        self._load_mask()
        
        # 数据路径配置 (对应readme中的2-8项)
        self.data_paths = {
            "historical": "Z:/yuan/paper3_new02/EPEs/historical/",
            "historical_models": "Z:/yuan/paper3_new02/EPEs/historical_models/",
            "EPEs_delta_models": "Z:/yuan/paper3_new02/EPEs/EPEs_delta_models/",
            "ssp_models_jz": "Z:/yuan/paper3_new02/EPEs/ssp_models_jz/",
            "ssp_mme_jz": "Z:/yuan/paper3_new02/EPEs/ssp_mme_jz/",
            "historical_mean": "Z:/yuan/paper3_new02/EPEs/historical_mean/",
            "ssp_mme_jz_5yr": "Z:/yuan/paper3_new02/EPEs/ssp_mme_jz_5yr/"
        }
    
    def _load_mask(self):
        """加载掩膜数据"""
        try:
            with rasterio.open(self.mask_path) as src:
                self.mask_data = src.read(1)
                self.mask_profile = src.profile.copy()
            logger.info(f"成功加载掩膜文件: {self.mask_path}")
            logger.info(f"掩膜数据形状: {self.mask_data.shape}")
            logger.info(f"有效像元数量: {np.sum(self.mask_data == 1)}")
        except Exception as e:
            logger.error(f"加载掩膜文件失败: {e}")
            raise
    
    def _apply_mask(self, data_array):
        """
        应用掩膜到数据数组
        
        Args:
            data_array: 输入数据数组
            
        Returns:
            掩膜后的数据数组
        """
        # 创建掩膜后的数据副本
        masked_data = data_array.copy()
        
        # 将掩膜为0的区域设置为nodata
        masked_data[self.mask_data != 1] = self.mask_profile['nodata']
        
        return masked_data
    
    def _process_single_file(self, input_file, output_file):
        """
        处理单个文件
        
        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径
        """
        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            
            with rasterio.open(input_file) as src:
                # 读取数据
                data = src.read(1)
                profile = src.profile.copy()
                
                # 检查数据形状是否与掩膜匹配
                if data.shape != self.mask_data.shape:
                    logger.warning(f"数据形状不匹配: {input_file} - {data.shape} vs {self.mask_data.shape}")
                    return False
                
                # 应用掩膜
                masked_data = self._apply_mask(data)
                
                # 更新profile
                if profile.get('nodata') is None:
                    profile['nodata'] = -9999
                
                # 写入输出文件
                with rasterio.open(output_file, 'w', **profile) as dst:
                    dst.write(masked_data, 1)
                
                logger.debug(f"成功处理: {input_file} -> {output_file}")
                return True
                
        except Exception as e:
            logger.error(f"处理文件失败 {input_file}: {e}")
            return False
    
    def _get_relative_path(self, full_path, base_path):
        """获取相对路径"""
        return os.path.relpath(full_path, base_path)
    
    def process_directory(self, data_type):
        """
        处理指定类型的数据目录
        
        Args:
            data_type: 数据类型键值
        """
        if data_type not in self.data_paths:
            logger.error(f"未知的数据类型: {data_type}")
            return
        
        input_base = self.data_paths[data_type]
        output_base = os.path.join(self.output_base_path, data_type)
        
        logger.info(f"开始处理 {data_type} 数据...")
        logger.info(f"输入路径: {input_base}")
        logger.info(f"输出路径: {output_base}")
        
        # 查找所有tif文件
        tif_pattern = os.path.join(input_base, "**", "*.tif")
        tif_files = glob.glob(tif_pattern, recursive=True)
        
        if not tif_files:
            logger.warning(f"在 {input_base} 中未找到tif文件")
            return
        
        logger.info(f"找到 {len(tif_files)} 个tif文件")
        
        # 处理每个文件
        success_count = 0
        for tif_file in tqdm(tif_files, desc=f"处理 {data_type}"):
            # 计算相对路径
            rel_path = self._get_relative_path(tif_file, input_base)
            output_file = os.path.join(output_base, rel_path)
            
            if self._process_single_file(tif_file, output_file):
                success_count += 1
        
        logger.info(f"{data_type} 处理完成: {success_count}/{len(tif_files)} 个文件成功")
    
    def process_all_data(self):
        """处理所有数据类型"""
        logger.info("开始处理所有EPE数据...")
        
        for data_type in self.data_paths.keys():
            try:
                self.process_directory(data_type)
            except Exception as e:
                logger.error(f"处理 {data_type} 时发生错误: {e}")
                continue
        
        logger.info("所有数据处理完成!")

def main():
    """主函数"""
    # 配置路径
    mask_path = "Z:/yuan/paper3_new02/shp/basin_Country_res/basin_025_1.tif"
    output_base_path = "Z:/yuan/paper3_new02/EPEs_clip"
    
    # 检查掩膜文件是否存在
    if not os.path.exists(mask_path):
        logger.error(f"掩膜文件不存在: {mask_path}")
        return
    
    # 创建处理器
    masker = EPEDataMasker(mask_path, output_base_path)
    
    # 处理所有数据
    masker.process_all_data()

if __name__ == "__main__":
    main()
