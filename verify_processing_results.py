#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证EPE数据掩膜处理结果的脚本
检查输出文件的完整性和正确性
"""

import os
import glob
import rasterio
import numpy as np
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProcessingVerifier:
    def __init__(self, output_base_path, mask_path):
        """
        初始化验证器
        
        Args:
            output_base_path: 输出基础路径
            mask_path: 掩膜文件路径
        """
        self.output_base_path = output_base_path
        self.mask_path = mask_path
        
        # 数据类型列表
        self.data_types = [
            "historical", "historical_models", "EPEs_delta_models",
            "ssp_models_jz", "ssp_mme_jz", "historical_mean", "ssp_mme_jz_5yr"
        ]
    
    def verify_single_file(self, file_path):
        """
        验证单个文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            dict: 验证结果
        """
        result = {
            'file': file_path,
            'exists': False,
            'readable': False,
            'has_data': False,
            'shape_correct': False,
            'has_nodata': False,
            'error': None
        }
        
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                result['error'] = "文件不存在"
                return result
            result['exists'] = True
            
            # 尝试读取文件
            with rasterio.open(file_path) as src:
                result['readable'] = True
                
                # 读取数据
                data = src.read(1)
                result['has_data'] = True
                
                # 检查形状 (假设应该与掩膜形状一致)
                with rasterio.open(self.mask_path) as mask_src:
                    mask_data = mask_src.read(1)
                    if data.shape == mask_data.shape:
                        result['shape_correct'] = True
                
                # 检查是否有nodata值
                if src.nodata is not None:
                    result['has_nodata'] = True
                
        except Exception as e:
            result['error'] = str(e)
        
        return result
    
    def verify_data_type(self, data_type):
        """
        验证指定数据类型的所有文件
        
        Args:
            data_type: 数据类型
            
        Returns:
            dict: 验证统计结果
        """
        logger.info(f"验证数据类型: {data_type}")
        
        data_dir = os.path.join(self.output_base_path, data_type)
        
        if not os.path.exists(data_dir):
            logger.warning(f"数据目录不存在: {data_dir}")
            return {
                'data_type': data_type,
                'total_files': 0,
                'valid_files': 0,
                'errors': ["数据目录不存在"]
            }
        
        # 查找所有tif文件
        tif_pattern = os.path.join(data_dir, "**", "*.tif")
        tif_files = glob.glob(tif_pattern, recursive=True)
        
        if not tif_files:
            logger.warning(f"在 {data_dir} 中未找到tif文件")
            return {
                'data_type': data_type,
                'total_files': 0,
                'valid_files': 0,
                'errors': ["未找到tif文件"]
            }
        
        # 验证每个文件
        valid_count = 0
        errors = []
        
        for tif_file in tif_files:
            result = self.verify_single_file(tif_file)
            
            if result['exists'] and result['readable'] and result['has_data'] and result['shape_correct']:
                valid_count += 1
            else:
                error_msg = f"{os.path.basename(tif_file)}: {result.get('error', '验证失败')}"
                errors.append(error_msg)
        
        return {
            'data_type': data_type,
            'total_files': len(tif_files),
            'valid_files': valid_count,
            'errors': errors
        }
    
    def verify_all_data(self):
        """验证所有数据类型"""
        logger.info("开始验证所有处理结果...")
        
        overall_stats = {
            'total_data_types': len(self.data_types),
            'processed_data_types': 0,
            'total_files': 0,
            'valid_files': 0,
            'data_type_results': []
        }
        
        for data_type in self.data_types:
            result = self.verify_data_type(data_type)
            overall_stats['data_type_results'].append(result)
            
            if result['total_files'] > 0:
                overall_stats['processed_data_types'] += 1
            
            overall_stats['total_files'] += result['total_files']
            overall_stats['valid_files'] += result['valid_files']
        
        return overall_stats
    
    def print_verification_report(self, stats):
        """打印验证报告"""
        logger.info("\n" + "="*60)
        logger.info("EPE数据处理结果验证报告")
        logger.info("="*60)
        
        logger.info(f"总数据类型: {stats['total_data_types']}")
        logger.info(f"已处理数据类型: {stats['processed_data_types']}")
        logger.info(f"总文件数: {stats['total_files']}")
        logger.info(f"有效文件数: {stats['valid_files']}")
        
        if stats['total_files'] > 0:
            success_rate = (stats['valid_files'] / stats['total_files']) * 100
            logger.info(f"成功率: {success_rate:.2f}%")
        
        logger.info("\n详细结果:")
        logger.info("-" * 60)
        
        for result in stats['data_type_results']:
            data_type = result['data_type']
            total = result['total_files']
            valid = result['valid_files']
            
            if total > 0:
                rate = (valid / total) * 100
                status = "✓" if rate == 100 else "⚠" if rate > 0 else "✗"
                logger.info(f"{status} {data_type}: {valid}/{total} ({rate:.1f}%)")
                
                if result['errors']:
                    for error in result['errors'][:3]:  # 只显示前3个错误
                        logger.info(f"    错误: {error}")
                    if len(result['errors']) > 3:
                        logger.info(f"    ... 还有 {len(result['errors']) - 3} 个错误")
            else:
                logger.info(f"✗ {data_type}: 未处理或无文件")

def main():
    """主函数"""
    output_base_path = "Z:/yuan/paper3_new02/EPEs_clip"
    mask_path = "Z:/yuan/paper3_new02/shp/basin_Country_res/basin_025_1.tif"
    
    # 检查基础路径
    if not os.path.exists(output_base_path):
        logger.error(f"输出基础路径不存在: {output_base_path}")
        return
    
    if not os.path.exists(mask_path):
        logger.error(f"掩膜文件不存在: {mask_path}")
        return
    
    # 创建验证器
    verifier = ProcessingVerifier(output_base_path, mask_path)
    
    # 执行验证
    stats = verifier.verify_all_data()
    
    # 打印报告
    verifier.print_verification_report(stats)

if __name__ == "__main__":
    main()
