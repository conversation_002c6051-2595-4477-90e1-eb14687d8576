#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
批量处理所有EPE数据的主脚本
按照任务顺序逐个处理数据类型
"""

import os
import time
from EPE_data_masking import EPEDataMasker
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('epe_processing.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def process_data_step_by_step():
    """按步骤处理所有数据类型"""
    
    # 配置路径
    mask_path = "Z:/yuan/paper3_new02/shp/basin_Country_res/basin_025_1.tif"
    output_base_path = "Z:/yuan/paper3_new02/EPEs_clip"
    
    # 检查掩膜文件是否存在
    if not os.path.exists(mask_path):
        logger.error(f"掩膜文件不存在: {mask_path}")
        return False
    
    # 数据处理顺序 (对应任务2-8)
    processing_order = [
        ("historical", "ERA5-Land历史极端降水指标数据"),
        ("historical_models", "CMIP6历史模式极端降水指标数据"),
        ("EPEs_delta_models", "偏差校正因子数据"),
        ("ssp_models_jz", "校正后未来情景极端降水数据"),
        ("ssp_mme_jz", "多模式集合结果数据"),
        ("historical_mean", "历史多年平均数据"),
        ("ssp_mme_jz_5yr", "未来逐五年平均数据")
    ]
    
    try:
        # 创建处理器
        masker = EPEDataMasker(mask_path, output_base_path)
        
        # 记录总体开始时间
        total_start_time = time.time()
        
        # 逐个处理数据类型
        for i, (data_type, description) in enumerate(processing_order, 1):
            logger.info(f"\n{'='*60}")
            logger.info(f"步骤 {i}/7: 开始处理 {description}")
            logger.info(f"数据类型: {data_type}")
            logger.info(f"{'='*60}")
            
            step_start_time = time.time()
            
            try:
                masker.process_directory(data_type)
                
                step_end_time = time.time()
                step_duration = step_end_time - step_start_time
                
                logger.info(f"步骤 {i} 完成! 耗时: {step_duration:.2f} 秒")
                
            except Exception as e:
                logger.error(f"步骤 {i} 处理失败: {e}")
                logger.info("继续处理下一个数据类型...")
                continue
        
        # 记录总体结束时间
        total_end_time = time.time()
        total_duration = total_end_time - total_start_time
        
        logger.info(f"\n{'='*60}")
        logger.info(f"所有数据处理完成!")
        logger.info(f"总耗时: {total_duration:.2f} 秒 ({total_duration/60:.2f} 分钟)")
        logger.info(f"{'='*60}")
        
        return True
        
    except Exception as e:
        logger.error(f"处理过程中发生严重错误: {e}")
        return False

def check_prerequisites():
    """检查运行前提条件"""
    logger.info("检查运行前提条件...")
    
    # 检查必要的Python包
    required_packages = ['rasterio', 'numpy', 'tqdm']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"缺少必要的Python包: {', '.join(missing_packages)}")
        logger.info("请使用以下命令安装:")
        logger.info(f"conda install {' '.join(missing_packages)}")
        return False
    
    # 检查掩膜文件
    mask_path = "Z:/yuan/paper3_new02/shp/basin_Country_res/basin_025_1.tif"
    if not os.path.exists(mask_path):
        logger.error(f"掩膜文件不存在: {mask_path}")
        return False
    
    # 检查输出目录的父目录是否存在
    output_parent = "Z:/yuan/paper3_new02"
    if not os.path.exists(output_parent):
        logger.error(f"输出路径的父目录不存在: {output_parent}")
        return False
    
    logger.info("前提条件检查通过!")
    return True

def main():
    """主函数"""
    logger.info("EPE数据掩膜处理程序启动")
    
    # 检查前提条件
    if not check_prerequisites():
        logger.error("前提条件检查失败，程序退出")
        return
    
    # 询问用户确认
    print("\n即将开始处理以下数据类型:")
    print("1. ERA5-Land历史极端降水指标数据")
    print("2. CMIP6历史模式极端降水指标数据") 
    print("3. 偏差校正因子数据")
    print("4. 校正后未来情景极端降水数据")
    print("5. 多模式集合结果数据")
    print("6. 历史多年平均数据")
    print("7. 未来逐五年平均数据")
    
    response = input("\n确认开始处理? (y/n): ").strip().lower()
    if response != 'y':
        logger.info("用户取消操作")
        return
    
    # 开始处理
    success = process_data_step_by_step()
    
    if success:
        logger.info("程序成功完成!")
    else:
        logger.error("程序执行过程中出现错误!")

if __name__ == "__main__":
    main()
