import os
import numpy as np
import rasterio
from pathlib import Path
import concurrent.futures


def compute_yearly_mean_for_index(index_name, input_dir, output_dir, years, profile):
    """
    计算单个指标的历史多年平均值
    """
    print(f"\n=== 计算{index_name}的历史多年平均值 ===")
    
    # 读取第一年的数据来获取形状信息
    first_year_file = os.path.join(input_dir, index_name, f"{index_name}_{years[0]}.tif")
    with rasterio.open(first_year_file) as src:
        first_data = src.read(1)
        m, n = first_data.shape
    
    # 初始化累积数组
    cumulative_data = np.zeros((m, n), dtype=np.float64)
    valid_count = np.zeros((m, n), dtype=np.int32)
    
    # 逐年份读取并累积
    for year in years:
        year_file = os.path.join(input_dir, index_name, f"{index_name}_{year}.tif")
        if os.path.exists(year_file):
            with rasterio.open(year_file) as src:
                year_data = src.read(1)
                # 只对非NaN值进行累积
                valid_mask = ~np.isnan(year_data)
                cumulative_data[valid_mask] += year_data[valid_mask]
                valid_count[valid_mask] += 1
            print(f"已处理{year}年{index_name}")
        else:
            print(f"警告：{year_file}不存在")
    
    # 计算平均值
    mean_data = np.full((m, n), np.nan, dtype=np.float32)
    valid_mask = valid_count > 0
    mean_data[valid_mask] = cumulative_data[valid_mask] / valid_count[valid_mask]
    
    # 保存结果
    output_file = os.path.join(output_dir, f"{index_name}_1971_2020_mean.tif")
    profile.update(dtype=rasterio.float32, count=1, compress='lzw')
    with rasterio.open(output_file, 'w', **profile) as dst:
        dst.write(mean_data, 1)
    
    print(f"已保存{index_name}多年平均值到: {output_file}")
    return output_file


def process_index_mean(args):
    """
    多进程处理单个指标的平均值计算
    """
    index_name, input_dir, output_dir, years, profile = args
    return compute_yearly_mean_for_index(index_name, input_dir, output_dir, years, profile)


if __name__ == '__main__':
    # 路径设置
    input_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/EPEs/historical'
    output_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/EPEs/historical_mean'
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 定义指标名称
    index_names = ['R90pD', 'R90pTOT', 'R95pD', 'R95pTOT', 'R99pD', 'R99pTOT', 
                   'R90pI', 'R95pI', 'R99pI', 'RX1-day']
    
    # 定义年份范围
    years = list(range(1971, 2021))  # 1971-2020年
    
    # 获取空间参考信息（从第一个指标的第一个年份文件）
    first_file = os.path.join(input_dir, index_names[0], f"{index_names[0]}_{years[0]}.tif")
    with rasterio.open(first_file) as src:
        profile = src.profile
    
    print(f"开始计算1971-2020年历史多年平均值...")
    print(f"输入目录: {input_dir}")
    print(f"输出目录: {output_dir}")
    print(f"处理年份: {years[0]}-{years[-1]} ({len(years)}年)")
    print(f"指标数量: {len(index_names)}")
    
    # 使用多进程并行计算
    args_list = [(index_name, input_dir, output_dir, years, profile) 
                 for index_name in index_names]
    
    with concurrent.futures.ProcessPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(process_index_mean, args) for args in args_list]
        
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                print(f"完成处理: {result}")
            except Exception as e:
                print(f"处理过程中出现错误: {e}")
    
    print(f"\n=== 所有指标的历史多年平均值计算完成 ===")
    print(f"结果保存在: {output_dir}")
    
    # 验证输出文件
    print("\n=== 输出文件验证 ===")
    for index_name in index_names:
        output_file = os.path.join(output_dir, f"{index_name}_1971_2020_mean.tif")
        if os.path.exists(output_file):
            with rasterio.open(output_file) as src:
                data = src.read(1)
                valid_pixels = np.sum(~np.isnan(data))
                print(f"{index_name}: {valid_pixels}个有效像元")
        else:
            print(f"{index_name}: 文件不存在")
