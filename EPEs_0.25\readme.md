# 极端降水事件(EPEs)分析处理系统

## 项目概述

本项目是一个基于ERA5-Land再分析数据和CMIP6气候模式数据的极端降水事件分析处理系统。系统通过Delta校正方法对CMIP6模式数据进行偏差校正，生成高质量的历史和未来极端降水指标数据集，支持气候变化影响评估和风险分析。

## 主要功能

- **极端降水指标计算**：基于分位数阈值计算10种极端降水指标
- **偏差校正**：使用Delta乘法校正方法校正CMIP6模式偏差
- **多模式集合**：计算7种统计量的多模式集合(MME)结果
- **时间尺度处理**：支持逐年和逐五年时间尺度分析
- **并行计算**：采用多进程并行处理提高计算效率

## 数据源

- **观测数据**：ERA5-Land再分析数据 (1971-2020年，0.25°空间分辨率)
- **模式数据**：NASA-CMIP6数据集，包含23个气候模式
- **情景数据**：4个共享社会经济路径(SSP126, SSP245, SSP370, SSP585)

## 极端降水指标

系统计算以下10种极端降水指标：

| 指标代码 | 指标名称 | 定义 | 单位 |
|---------|---------|------|------|
| R90pD | 极端降水日数(90分位数) | 日降水量超过90分位数阈值的天数 | 天 |
| R90pTOT | 极端降水总量(90分位数) | 日降水量超过90分位数阈值的降水总量 | mm |
| R95pD | 极端降水日数(95分位数) | 日降水量超过95分位数阈值的天数 | 天 |
| R95pTOT | 极端降水总量(95分位数) | 日降水量超过95分位数阈值的降水总量 | mm |
| R99pD | 极端降水日数(99分位数) | 日降水量超过99分位数阈值的天数 | 天 |
| R99pTOT | 极端降水总量(99分位数) | 日降水量超过99分位数阈值的降水总量 | mm |
| R90pI | 极端降水强度(90分位数) | R90pTOT/R90pD | mm/天 |
| R95pI | 极端降水强度(95分位数) | R95pTOT/R95pD | mm/天 |
| R99pI | 极端降水强度(99分位数) | R99pTOT/R99pD | mm/天 |
| RX1-day | 年最大日降水量 | 一年中单日最大降水量 | mm |

## 计算流程

### 第一步：ERA5-Land历史极端降水计算 (`step1_EPEs_his.py`)
- **时间范围**：1971-2020年
- **输入数据**：ERA5-Land日降水量MAT文件
- **阈值文件**：90、95、99分位数阈值
- **输出**：历史逐年10种极端降水指标

### 第二步：CMIP6历史极端降水计算 (`step2_EPEs_his_models.py`)
- **时间范围**：1971-2014年
- **模式数量**：23个CMIP6模式
- **输入数据**：CMIP6历史试验日降水量数据
- **输出**：各模式历史逐年极端降水指标

### 第三步：偏差校正因子计算 (`step3_EPEs_delta_models.py`)
- **校正方法**：Delta乘法校正
- **校正公式**：correction = obs_mean / model_mean
- **约束条件**：校正因子限制在[0.1, 10.0]范围内
- **输出**：各模式各指标的校正因子

### 第四步：未来情景极端降水校正 (`step4_EPEs_ssp_models.py`, `step4_EPEs_ssp_models_jz.py`)
- **时间范围**：2021-2100年
- **情景**：SSP126, SSP245, SSP370, SSP585
- **校正方法**：应用第三步计算的校正因子
- **输出**：校正后的未来逐年极端降水指标

### 第五步：多模式集合计算 (`step5_EPEs_ssp_jz_MME.py`, `step5_EPEs_ssp_jz_MME_mean_min_max.py`)
- **统计量**：7种MME统计量
  - Mean：多模式平均值
  - Max：多模式最大值  
  - Min：多模式最小值
  - P95：95%分位数
  - P05：5%分位数
  - MeanPstd：平均值+标准差
  - MeanMstd：平均值-标准差
- **输出**：各情景各统计量的逐年极端降水指标

### 第六步：历史多年平均计算 (`step6_EPEs_his_yearsmean.py`)
- **时间范围**：1971-2020年
- **计算方法**：50年平均值
- **输出**：ERA5-Land历史多年平均极端降水指标

### 第七步：未来逐五年平均计算 (`step7_EPEs_ssp_yearsmean.py`)
- **时间范围**：2021-2100年，逐五年(如2025代表2021-2025年平均)
- **处理对象**：各情景7种MME统计量
- **输出**：逐五年平均极端降水指标

### 第八步：流域统计分析 (`step8_EPEs_his_tj.py`)
- **功能**：基于流域边界进行空间统计
- **输入**：历史多年平均极端降水指标
- **输出**：各流域的极端降水统计值

## 技术规格

### 空间分辨率
- **网格分辨率**：0.25° × 0.25°
- **空间范围**：全球陆地区域
- **网格尺寸**：600 × 1440 (纬度 × 经度)

### 时间分辨率
- **输入数据**：日尺度
- **输出产品**：年尺度、五年平均

### 数据格式
- **输入格式**：MAT文件(MATLAB格式)、TIFF文件
- **输出格式**：GeoTIFF文件，LZW压缩
- **数据类型**：Float32

### CMIP6模式列表

本项目使用以下23个CMIP6气候模式：

| 序号 | 模式名称 | 机构 |
|------|----------|------|
| 1 | ACCESS-CM2 | CSIRO-ARCCSS |
| 2 | ACCESS-ESM1-5 | CSIRO |
| 3 | BCC-CSM2-MR | BCC |
| 4 | CanESM5 | CCCma |
| 5 | CMCC-ESM2 | CMCC |
| 6 | CNRM-CM6-1 | CNRM-CERFACS |
| 7 | CNRM-ESM2-1 | CNRM-CERFACS |
| 8 | EC-Earth3 | EC-Earth-Consortium |
| 9 | EC-Earth3-Veg-LR | EC-Earth-Consortium |
| 10 | FGOALS-g3 | CAS |
| 11 | GFDL-ESM4 | NOAA-GFDL |
| 12 | GISS-E2-1-G | NASA-GISS |
| 13 | INM-CM4-8 | INM |
| 14 | INM-CM5-0 | INM |
| 15 | IPSL-CM6A-LR | IPSL |
| 16 | MIROC6 | MIROC |
| 17 | MIROC-ES2L | MIROC |
| 18 | MPI-ESM1-2-HR | MPI-M |
| 19 | MPI-ESM1-2-LR | MPI-M |
| 20 | MRI-ESM2-0 | MRI |
| 21 | NorESM2-LM | NCC |
| 22 | NorESM2-MM | NCC |
| 23 | TaiESM1 | AS-RCEC |

## 数据存储结构

### 1. 阈值文件
**描述**：ERA5-Land 1971-2020年极端降水分位数阈值
```
/mnt/sdb2/yuanshuai/wanpan/yuan/ERA5-Land/fbl_025/EPEs/yz_600_1440/
├── pre_yz_90_1971_2020.tif    # 90分位数阈值
├── pre_yz_95_1971_2020.tif    # 95分位数阈值
└── pre_yz_99_1971_2020.tif    # 99分位数阈值
```

### 2. ERA5-Land历史极端降水指标 (1971-2020年)
**路径**：`/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/EPEs/historical/`
```
historical/
├── R90pD/
│   ├── R90pD_1971.tif
│   ├── R90pD_1972.tif
│   └── ...
├── R90pTOT/
├── R95pD/
├── R95pTOT/
├── R99pD/
├── R99pTOT/
├── R90pI/
├── R95pI/
├── R99pI/
└── RX1-day/
```

### 3. CMIP6历史模式极端降水指标 (1971-2014年)
**路径**：`/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/EPEs/historical_models/`
```
historical_models/
├── ACCESS-CM2/
│   ├── R90pD/
│   │   ├── R90pD_1971.tif
│   │   └── ...
│   ├── R90pTOT/
│   └── ...
├── ACCESS-ESM1-5/
└── ...
```

### 4. 偏差校正因子
**路径**：`/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/EPEs/EPEs_delta_models/`
```
EPEs_delta_models/
├── ACCESS-CM2/
│   ├── R90pD_correction.tif
│   ├── R90pTOT_correction.tif
│   └── ...
├── ACCESS-ESM1-5/
└── ...
```

### 5. 校正后未来情景极端降水 (2021-2100年)
**路径**：`/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/EPEs/ssp_models_jz/`
```
ssp_models_jz/
├── ssp126/
│   ├── ACCESS-CM2/
│   │   ├── R90pD/
│   │   │   ├── R90pD_2021.tif
│   │   │   └── ...
│   │   └── ...
│   └── ...
├── ssp245/
├── ssp370/
└── ssp585/
```

### 6. 多模式集合结果 (2021-2100年)
**路径**：`/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/EPEs/ssp_mme_jz/`
```
ssp_mme_jz/
├── ssp126/
│   ├── Mean/
│   │   ├── R90pD/
│   │   │   ├── R90pD_2021.tif
│   │   │   └── ...
│   │   └── ...
│   ├── Max/
│   ├── Min/
│   ├── P95/
│   ├── P05/
│   ├── MeanPstd/
│   └── MeanMstd/
└── ...
```

### 7. 历史多年平均 (1971-2020年)
**路径**：`/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/EPEs/historical_mean/`
```
historical_mean/
├── R90pD_1971_2020_mean.tif
├── R90pTOT_1971_2020_mean.tif
└── ...
```

### 8. 未来逐五年平均 (2025-2100年)
**路径**：`/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/EPEs/ssp_mme_jz_5yr/`
```
ssp_mme_jz_5yr/
├── ssp126/
│   ├── Mean/
│   │   ├── R90pD/
│   │   │   ├── R90pD_2025.tif  # 2021-2025年平均
│   │   │   ├── R90pD_2030.tif  # 2026-2030年平均
│   │   │   └── ...
│   │   └── ...
│   └── ...
└── ...
```

## 运行环境要求

### Python依赖包
```python
numpy>=1.21.0
rasterio>=1.3.0
h5py>=3.7.0
geopandas>=0.12.0
pandas>=1.5.0
rasterstats>=0.17.0
concurrent.futures  # Python标准库
```

### 系统要求
- **操作系统**：Linux (推荐)
- **内存**：建议32GB以上
- **存储空间**：建议1TB以上可用空间
- **CPU**：支持多核并行处理

## 使用方法

### 1. 环境配置
```bash
# 使用conda创建环境
conda create -n epes python=3.9
conda activate epes

# 安装依赖包
conda install numpy rasterio h5py geopandas pandas
pip install rasterstats
```

### 2. 按步骤运行
```bash
# 第一步：计算ERA5-Land历史极端降水
python step1_EPEs_his.py

# 第二步：计算CMIP6历史极端降水
python step2_EPEs_his_models.py

# 第三步：计算校正因子
python step3_EPEs_delta_models.py

# 第四步：校正未来情景数据
python step4_EPEs_ssp_models.py
python step4_EPEs_ssp_models_jz.py

# 第五步：计算多模式集合
python step5_EPEs_ssp_jz_MME.py
python step5_EPEs_ssp_jz_MME_mean_min_max.py

# 第六步：计算历史多年平均
python step6_EPEs_his_yearsmean.py

# 第七步：计算未来逐五年平均
python step7_EPEs_ssp_yearsmean.py

# 第八步：流域统计分析
python step8_EPEs_his_tj.py
```

### 3. 配置说明
在运行前需要根据实际情况修改各脚本中的路径配置：
- 输入数据路径
- 输出数据路径
- 阈值文件路径
- 并行进程数量

## 核心算法

### Delta偏差校正方法
```python
# 校正公式
correction_factor = obs_mean / model_mean

# 约束条件
correction_factor = np.clip(correction_factor, 0.1, 10.0)

# 特殊情况处理
if model_mean == 0 and obs_mean == 0:
    correction_factor = 1.0
elif model_mean == 0 and obs_mean > 0:
    correction_factor = 10.0
elif model_mean > 0 and obs_mean == 0:
    correction_factor = 0.1
```

### 多模式集合统计
```python
# 7种MME统计量计算
mme_mean = np.nanmean(model_data, axis=0)
mme_max = np.nanmax(model_data, axis=0)
mme_min = np.nanmin(model_data, axis=0)
mme_p95 = np.nanpercentile(model_data, 95, axis=0)
mme_p05 = np.nanpercentile(model_data, 5, axis=0)
mme_std = np.nanstd(model_data, axis=0)
mme_mean_plus_std = mme_mean + mme_std
mme_mean_minus_std = mme_mean - mme_std
```

## 性能优化

### 并行处理
- 使用`concurrent.futures.ProcessPoolExecutor`进行多进程并行
- 年份级别并行处理，提高计算效率
- 可根据系统配置调整`max_workers`参数

### 内存管理
- 逐年处理数据，避免一次性加载所有年份
- 使用`np.float32`数据类型减少内存占用
- 及时释放不需要的数组变量

### 存储优化
- 输出文件使用LZW压缩减少存储空间
- 按指标分文件夹存储，便于管理和访问

## 质量控制

### 数据验证
- 检查输入数据的完整性和有效性
- 验证阈值文件的空间一致性
- 确保时间序列的连续性

### 结果检查
- 校正因子合理性检查(0.1-10.0范围)
- 极端降水指标的物理合理性验证
- 多模式集合结果的统计一致性检查

## 注意事项

1. **路径配置**：运行前请确保所有输入数据路径正确
2. **存储空间**：确保有足够的磁盘空间存储输出结果
3. **计算资源**：大规模数据处理需要充足的内存和CPU资源
4. **数据格式**：确保输入数据格式与代码要求一致
5. **时间范围**：注意不同步骤的时间范围差异

## 输出产品应用

### 气候变化研究
- 极端降水事件的历史变化趋势分析
- 未来气候情景下的极端降水预估
- 不同排放路径的影响对比

### 风险评估
- 洪涝灾害风险评估
- 基础设施气候风险分析
- 农业干旱和洪涝风险评估

### 政策支持
- 气候适应策略制定
- 防灾减灾规划
- 可持续发展目标监测

## 版本信息

- **当前版本**：v1.0
- **最后更新**：2024年
- **兼容性**：Python 3.8+

## 联系方式

如有问题或建议，请联系项目维护者。

---

**免责声明**：本系统仅供科研使用，使用者需要根据具体应用场景验证结果的适用性。
