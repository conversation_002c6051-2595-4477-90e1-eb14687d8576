{"cells": [{"cell_type": "code", "execution_count": 1, "id": "eedfb2b7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["库导入完成！\n"]}], "source": ["# 导入必要的库\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "import geopandas as gpd\n", "import rasterio\n", "from rasterio.features import rasterize\n", "from rasterio.mask import mask\n", "from rasterio.transform import from_bounds\n", "from shapely.geometry import box\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"库导入完成！\")\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "id": "4f6d8cca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据路径设置完成！\n", "正在读取栅格数据...\n", "栅格数据读取完成\n", "栅格坐标系：EPSG:4326\n", "栅格形状：(600, 1440)\n", "有效像元数量（值为1）：19229\n"]}], "source": ["# 数据路径设置\n", "basin_raster_path = r\"Z:\\yuan\\paper3_new02\\shp\\basin_Country_res\\basin_025_1.tif\"\n", "country_shp_path = r\"Z:\\yuan\\paper3_new02\\shp\\basin_Country_res\\basin_Country.shp\"\n", "output_dir = r\"Z:\\yuan\\paper3_new02\\shp\\basin_Country_res\"\n", "\n", "print(\"数据路径设置完成！\")\n", "\n", "# 第一步：读取栅格数据\n", "print(\"正在读取栅格数据...\")\n", "with rasterio.open(basin_raster_path) as src:\n", "    basin_data = src.read(1)  # 读取第一个波段\n", "    raster_profile = src.profile.copy()\n", "    raster_transform = src.transform\n", "    raster_crs = src.crs\n", "    raster_shape = src.shape\n", "    \n", "print(f\"栅格数据读取完成\")\n", "print(f\"栅格坐标系：{raster_crs}\")\n", "print(f\"栅格形状：{raster_shape}\")\n", "print(f\"有效像元数量（值为1）：{np.sum(basin_data == 1)}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "id": "5f865387", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "正在读取国家边界数据...\n", "国家边界数据读取完成，共45个国家\n", "国家边界坐标系：EPSG:4326\n", "国家ID字段：NR_C_ID\n", "国家ID范围：93 - 99139\n"]}], "source": ["# 第二步：读取国家边界数据\n", "print(\"\\n正在读取国家边界数据...\")\n", "countries_gdf = gpd.read_file(country_shp_path)\n", "print(f\"国家边界数据读取完成，共{len(countries_gdf)}个国家\")\n", "print(f\"国家边界坐标系：{countries_gdf.crs}\")\n", "print(f\"国家ID字段：NR_C_ID\")\n", "print(f\"国家ID范围：{countries_gdf['NR_C_ID'].min()} - {countries_gdf['NR_C_ID'].max()}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "id": "c3231ebc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "开始创建国家ID栅格...\n", "需要处理的有效像元数量：19229\n", "国家ID 120: 分配了 34 个像元\n", "国家ID 128: 分配了 15 个像元\n", "国家ID 111: 分配了 15 个像元\n", "国家ID 105: 分配了 7 个像元\n", "国家ID 109: 分配了 121 个像元\n", "国家ID 127: 分配了 258 个像元\n", "国家ID 108: 分配了 249 个像元\n", "国家ID 154: 分配了 636 个像元\n", "国家ID 102: 分配了 66 个像元\n", "国家ID 220: 分配了 331 个像元\n", "国家ID 99139: 分配了 41 个像元\n", "国家ID 98139: 分配了 41 个像元\n", "国家ID 114: 分配了 440 个像元\n", "国家ID 7185: 分配了 588 个像元\n", "国家ID 219: 分配了 148 个像元\n", "国家ID 126: 分配了 39 个像元\n", "国家ID 216: 分配了 668 个像元\n", "国家ID 129: 分配了 447 个像元\n", "国家ID 117: 分配了 37 个像元\n", "国家ID 157: 分配了 24 个像元\n", "国家ID 141: 分配了 1731 个像元\n", "国家ID 707: 分配了 496 个像元\n", "国家ID 138: 分配了 4 个像元\n", "国家ID 107: 分配了 10 个像元\n", "国家ID 101: 分配了 77 个像元\n", "国家ID 95: 分配了 1 个像元\n", "国家ID 144: 分配了 618 个像元\n", "国家ID 163: 分配了 60 个像元\n", "国家ID 8330: 分配了 286 个像元\n", "国家ID 93: 分配了 94 个像元\n", "国家ID 98: 分配了 2 个像元\n", "国家ID 218: 分配了 593 个像元\n", "国家ID 104: 分配了 161 个像元\n", "国家ID 115: 分配了 193 个像元\n", "国家ID 121: 分配了 182 个像元\n", "国家ID 149: 分配了 1034 个像元\n", "国家ID 100: 分配了 49 个像元\n", "国家ID 5889: 分配了 1188 个像元\n", "国家ID 225: 分配了 210 个像元\n", "国家ID 213: 分配了 1833 个像元\n", "国家ID 221: 分配了 96 个像元\n", "国家ID 103: 分配了 120 个像元\n", "国家ID 9637: 分配了 5926 个像元\n", "国家ID 135: 分配了 55 个像元\n", "国家ID 2594: 分配了 24 个像元\n", "\n", "总共处理了 19248 个有效像元\n"]}], "source": ["# 第三步：创建国家ID栅格\n", "print(\"\\n开始创建国家ID栅格...\")\n", "\n", "# 初始化输出栅格，使用nodata值\n", "country_id_raster = np.full(raster_shape, raster_profile['nodata'], dtype=np.float32)\n", "\n", "# 找到有效像元位置（值为1的像元）\n", "valid_mask = basin_data == 1\n", "print(f\"需要处理的有效像元数量：{np.sum(valid_mask)}\")\n", "\n", "# 为每个国家分配ID到对应像元\n", "processed_pixels = 0\n", "\n", "for idx, country in countries_gdf.iterrows():\n", "    country_id = country['NR_C_ID']\n", "    country_geom = country.geometry\n", "    \n", "    try:\n", "        # 创建国家掩膜\n", "        country_mask = rasterize(\n", "            [country_geom],\n", "            out_shape=raster_shape,\n", "            transform=raster_transform,\n", "            fill=0,\n", "            default_value=1,\n", "            dtype=np.uint8\n", "        )\n", "        \n", "        # 找到同时满足有效像元和国家范围的像元\n", "        country_valid_mask = valid_mask & (country_mask == 1)\n", "        country_pixel_count = np.sum(country_valid_mask)\n", "        \n", "        if country_pixel_count > 0:\n", "            # 在这些位置赋值国家ID\n", "            country_id_raster[country_valid_mask] = country_id\n", "            processed_pixels += country_pixel_count\n", "            print(f\"国家ID {country_id}: 分配了 {country_pixel_count} 个像元\")\n", "        \n", "    except Exception as e:\n", "        print(f\"处理国家ID {country_id} 时出错: {e}\")\n", "\n", "print(f\"\\n总共处理了 {processed_pixels} 个有效像元\")\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "id": "c2b2fcfb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "保存国家ID栅格...\n", "国家ID栅格已保存：Z:\\yuan\\paper3_new02\\shp\\basin_Country_res\\country_id_raster.tif\n"]}], "source": ["# 第四步：保存结果栅格\n", "print(\"\\n保存国家ID栅格...\")\n", "output_path = os.path.join(output_dir, \"country_id_raster.tif\")\n", "\n", "# 更新profile\n", "output_profile = raster_profile.copy()\n", "output_profile.update({\n", "    'dtype': rasterio.float32,\n", "    'nodata': -9999,\n", "    'compress': 'lzw'\n", "})\n", "\n", "# 将原始nodata值替换为-9999\n", "country_id_raster[country_id_raster == raster_profile['nodata']] = -9999\n", "\n", "with rasterio.open(output_path, 'w', **output_profile) as dst:\n", "    dst.write(country_id_raster, 1)\n", "\n", "print(f\"国家ID栅格已保存：{output_path}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "id": "b07ddd1b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "验证结果...\n", "栅格中包含的国家ID数量：44\n", "国家ID范围：93 - 98139\n", "分配了国家ID的像元数量：19177\n", "\n", "前10个像元数量最多的国家：\n", "    Country_ID  Pixel_Count\n", "42        9637         5926\n", "30         213         1833\n", "24         141         1731\n", "39        5889         1186\n", "26         149         1034\n", "31         216          668\n", "27         154          634\n", "25         144          618\n", "32         218          593\n", "40        7185          583\n", "\n", "=== 任务完成 ===\n", "成功将19177个有效像元赋值为对应的国家ID\n", "输出文件：Z:\\yuan\\paper3_new02\\shp\\basin_Country_res\\country_id_raster.tif\n"]}], "source": ["# 第五步：验证结果\n", "print(\"\\n验证结果...\")\n", "unique_ids = np.unique(country_id_raster[country_id_raster != -9999])\n", "print(f\"栅格中包含的国家ID数量：{len(unique_ids)}\")\n", "print(f\"国家ID范围：{unique_ids.min():.0f} - {unique_ids.max():.0f}\")\n", "print(f\"分配了国家ID的像元数量：{np.sum(country_id_raster != -9999)}\")\n", "\n", "# 统计每个国家的像元数量\n", "country_pixel_counts = []\n", "for country_id in unique_ids:\n", "    pixel_count = np.sum(country_id_raster == country_id)\n", "    country_pixel_counts.append({\n", "        'Country_ID': int(country_id),\n", "        'Pixel_Count': pixel_count\n", "    })\n", "\n", "country_stats_df = pd.DataFrame(country_pixel_counts)\n", "country_stats_df = country_stats_df.sort_values('Pixel_Count', ascending=False)\n", "\n", "print(f\"\\n前10个像元数量最多的国家：\")\n", "print(country_stats_df.head(10))\n", "\n", "print(f\"\\n=== 任务完成 ===\")\n", "print(f\"成功将{np.sum(country_id_raster != -9999)}个有效像元赋值为对应的国家ID\")\n", "print(f\"输出文件：{output_path}\")"]}, {"cell_type": "code", "execution_count": 7, "id": "63c656fd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 检查缺失的国家ID ===\n", "shapefile中总国家数：45\n", "栅格中包含的国家数：44\n", "缺失的国家数：1\n", "\n", "缺失的国家ID列表：\n", "  国家ID: 99139, 国家名: Unknown\n"]}], "source": ["# 第六步：检查缺失的国家ID\n", "print(\"\\n=== 检查缺失的国家ID ===\")\n", "\n", "# 获取shapefile中所有的国家ID\n", "all_country_ids = set(countries_gdf['NR_C_ID'].values)\n", "print(f\"shapefile中总国家数：{len(all_country_ids)}\")\n", "\n", "# 获取栅格中包含的国家ID\n", "raster_country_ids = set(unique_ids)\n", "print(f\"栅格中包含的国家数：{len(raster_country_ids)}\")\n", "\n", "# 找出缺失的国家ID\n", "missing_country_ids = all_country_ids - raster_country_ids\n", "print(f\"缺失的国家数：{len(missing_country_ids)}\")\n", "\n", "if len(missing_country_ids) > 0:\n", "    print(f\"\\n缺失的国家ID列表：\")\n", "    missing_ids_sorted = sorted(list(missing_country_ids))\n", "    for missing_id in missing_ids_sorted:\n", "        # 查找国家名称（如果有NAME字段）\n", "        country_info = countries_gdf[countries_gdf['NR_C_ID'] == missing_id]\n", "        if not country_info.empty:\n", "            country_name = country_info.iloc[0].get('NAME', 'Unknown')\n", "            print(f\"  国家ID: {missing_id}, 国家名: {country_name}\")\n", "        else:\n", "            print(f\"  国家ID: {missing_id}\")\n", "else:\n", "    print(\"✓ 所有国家都已包含在栅格中\")\n"]}, {"cell_type": "code", "execution_count": 8, "id": "df01df12", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 对比有效像元数量 ===\n", "原始栅格(basin_025_1.tif)有效像元数：19229\n", "输出栅格(country_id_raster.tif)有效像元数：19177\n", "像元数量差异：52\n", "⚠️ 输出栅格缺少 52 个有效像元\n", "缺失像元位置分析：\n", "  实际缺失像元数：52\n", "  前5个缺失像元的行列坐标：\n", "    行160, 列913\n", "    行166, 列801\n", "    行173, 列805\n", "    行175, 列851\n", "    行175, 列852\n", "\n", "=== 详细统计报告 ===\n", "处理统计：\n", "  输入栅格有效像元：19229\n", "  成功处理像元：19248\n", "  输出栅格有效像元：19177\n", "  处理成功率：99.73%\n", "\n", "国家覆盖统计：\n", "  总国家数：45\n", "  有像元的国家数：44\n", "  无像元的国家数：1\n", "  国家覆盖率：97.78%\n", "\n", "统计结果已保存：Z:\\yuan\\paper3_new02\\shp\\basin_Country_res\\country_id_assignment_stats.csv\n", "\n", "=== 完整任务完成 ===\n"]}], "source": ["\n", "# 第七步：对比有效像元数量\n", "print(\"\\n=== 对比有效像元数量 ===\")\n", "\n", "# 原始栅格的有效像元数量\n", "original_valid_count = np.sum(basin_data == 1)\n", "print(f\"原始栅格(basin_025_1.tif)有效像元数：{original_valid_count}\")\n", "\n", "# 输出栅格的有效像元数量\n", "output_valid_count = np.sum(country_id_raster != -9999)\n", "print(f\"输出栅格(country_id_raster.tif)有效像元数：{output_valid_count}\")\n", "\n", "# 计算差异\n", "pixel_difference = original_valid_count - output_valid_count\n", "print(f\"像元数量差异：{pixel_difference}\")\n", "\n", "if pixel_difference == 0:\n", "    print(\"✓ 有效像元数量完全一致\")\n", "elif pixel_difference > 0:\n", "    print(f\"⚠️ 输出栅格缺少 {pixel_difference} 个有效像元\")\n", "    \n", "    # 分析缺失像元的位置\n", "    original_valid_mask = basin_data == 1\n", "    output_valid_mask = country_id_raster != -9999\n", "    missing_pixels_mask = original_valid_mask & (~output_valid_mask)\n", "    \n", "    print(f\"缺失像元位置分析：\")\n", "    missing_pixel_count = np.sum(missing_pixels_mask)\n", "    print(f\"  实际缺失像元数：{missing_pixel_count}\")\n", "    \n", "    if missing_pixel_count > 0:\n", "        # 找到缺失像元的坐标\n", "        missing_rows, missing_cols = np.where(missing_pixels_mask)\n", "        print(f\"  前5个缺失像元的行列坐标：\")\n", "        for i in range(min(5, len(missing_rows))):\n", "            row, col = missing_rows[i], missing_cols[i]\n", "            print(f\"    行{row}, 列{col}\")\n", "            \n", "elif pixel_difference < 0:\n", "    print(f\"⚠️ 输出栅格多出 {abs(pixel_difference)} 个有效像元\")\n", "\n", "# 第八步：详细统计报告\n", "print(\"\\n=== 详细统计报告 ===\")\n", "print(f\"处理统计：\")\n", "print(f\"  输入栅格有效像元：{original_valid_count}\")\n", "print(f\"  成功处理像元：{processed_pixels}\")\n", "print(f\"  输出栅格有效像元：{output_valid_count}\")\n", "print(f\"  处理成功率：{(output_valid_count/original_valid_count)*100:.2f}%\")\n", "\n", "print(f\"\\n国家覆盖统计：\")\n", "print(f\"  总国家数：{len(all_country_ids)}\")\n", "print(f\"  有像元的国家数：{len(raster_country_ids)}\")\n", "print(f\"  无像元的国家数：{len(missing_country_ids)}\")\n", "print(f\"  国家覆盖率：{(len(raster_country_ids)/len(all_country_ids))*100:.2f}%\")\n", "\n", "# 保存统计结果到CSV\n", "stats_output_path = os.path.join(output_dir, \"country_id_assignment_stats.csv\")\n", "country_stats_df.to_csv(stats_output_path, index=False, encoding='utf-8-sig')\n", "print(f\"\\n统计结果已保存：{stats_output_path}\")\n", "\n", "print(f\"\\n=== 完整任务完成 ===\")"]}], "metadata": {"kernelspec": {"display_name": "gee", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}