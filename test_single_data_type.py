#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试单个数据类型的掩膜处理脚本
用于验证处理逻辑是否正确
"""

import os
import sys
from EPE_data_masking import EPEDataMasker
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_single_data_type(data_type):
    """
    测试单个数据类型的处理
    
    Args:
        data_type: 要测试的数据类型
    """
    # 配置路径
    mask_path = "Z:/yuan/paper3_new02/shp/basin_Country_res/basin_025_1.tif"
    output_base_path = "Z:/yuan/paper3_new02/EPEs_clip"
    
    # 检查掩膜文件是否存在
    if not os.path.exists(mask_path):
        logger.error(f"掩膜文件不存在: {mask_path}")
        return False
    
    try:
        # 创建处理器
        masker = EPEDataMasker(mask_path, output_base_path)
        
        # 处理指定数据类型
        masker.process_directory(data_type)
        
        logger.info(f"数据类型 {data_type} 处理完成")
        return True
        
    except Exception as e:
        logger.error(f"处理数据类型 {data_type} 时发生错误: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法: python test_single_data_type.py <data_type>")
        print("可用的数据类型:")
        print("  - historical")
        print("  - historical_models") 
        print("  - EPEs_delta_models")
        print("  - ssp_models_jz")
        print("  - ssp_mme_jz")
        print("  - historical_mean")
        print("  - ssp_mme_jz_5yr")
        return
    
    data_type = sys.argv[1]
    
    # 验证数据类型
    valid_types = [
        "historical", "historical_models", "EPEs_delta_models",
        "ssp_models_jz", "ssp_mme_jz", "historical_mean", "ssp_mme_jz_5yr"
    ]
    
    if data_type not in valid_types:
        logger.error(f"无效的数据类型: {data_type}")
        logger.info(f"有效的数据类型: {', '.join(valid_types)}")
        return
    
    # 测试处理
    success = test_single_data_type(data_type)
    
    if success:
        logger.info("测试成功完成!")
    else:
        logger.error("测试失败!")

if __name__ == "__main__":
    main()
