import os
import numpy as np
import rasterio

def get_5year_periods(start, end, step=5):
    periods = []
    for y in range(start, end+1, step):
        y_end = min(y+step-1, end)
        periods.append((y, y_end))
    return periods

def process_5year_mme_from_mme(year_period, mme_dir, output_dir, index_names, stat_modes, profile):
    y_start, y_end = year_period
    print(f"\n=== 处理{y_start}-{y_end}年MME 5年段 ===")
    for stat in stat_modes:
        for idx_name in index_names:
            # 收集5年内每年的MME tif
            year_data = []
            for year in range(y_start, y_end+1):
                tif_path = os.path.join(mme_dir, stat, idx_name, f"{idx_name}_{year}.tif")
                if os.path.exists(tif_path):
                    with rasterio.open(tif_path) as src:
                        data = src.read(1)
                        year_data.append(data)
                else:
                    print(f"文件不存在: {tif_path}")
            if len(year_data) == (y_end-y_start+1):
                # 5年都存在才计算
                data_5yr = np.nanmean(np.array(year_data), axis=0)
                out_dir = os.path.join(output_dir, stat, idx_name)
                os.makedirs(out_dir, exist_ok=True)
                out_tif = os.path.join(out_dir, f"{idx_name}_{y_end}.tif")
                profile.update(dtype=rasterio.float32, count=1, compress='lzw')
                with rasterio.open(out_tif, 'w', **profile) as dst:
                    dst.write(data_5yr.astype(np.float32), 1)
                print(f"已保存{y_start}-{y_end}年 {stat} {idx_name} 5年段: {out_tif}")
            else:
                print(f"警告: {y_start}-{y_end}年 {stat} {idx_name} 有年份缺失，未输出")

if __name__ == '__main__':
    # 指标和统计量
    index_names = ['R90pD','R90pTOT','R95pD','R95pTOT','R99pD','R99pTOT','R90pI','R95pI','R99pI','RX1-day']
    stat_modes = ['Mean', 'Max', 'Min', 'P95', 'P05', 'MeanPstd', 'MeanMstd']
    ssps = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
    start_year = 2021
    end_year = 2100
    periods = get_5year_periods(start_year, end_year, step=5)
    # 以Mean的某一年tif为模板获取profile
    example_tif = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/EPEs/ssp_mme_jz/ssp126/Mean/R90pD/R90pD_2021.tif'
    with rasterio.open(example_tif) as src:
        profile = src.profile
    for ssp in ssps:
        mme_dir = f'/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/EPEs/ssp_mme_jz/{ssp}'
        output_dir = f'/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/EPEs/ssp_mme_jz_5yr/{ssp}'
        os.makedirs(output_dir, exist_ok=True)
        for period in periods:
            process_5year_mme_from_mme(period, mme_dir, output_dir, index_names, stat_modes, profile)
        print(f"完成情景 {ssp} 的5年段MME统计")
    print("\n=== 所有情景5年段MME统计完成 ===")
