import os
import geopandas as gpd
import rasterio
from rasterstats import zonal_stats
import pandas as pd

# 指标tif目录
TIF_DIR = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/EPEs/historical_mean'
# 指标名
INDEX_NAMES = ['R90pD', 'R90pTOT', 'R95pD', 'R95pTOT', 'R99pD', 'R99pTOT',
               'R90pI', 'R95pI', 'R99pI', 'RX1-day']
# shp路径
SHP_PATHS = [
    '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/shp/basins_lev01-12/basins_lev05.shp',
    '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/shp/basins_lev01-12/basins_lev08.shp'
]

if __name__ == '__main__':
    # 新增：输出目录
    OUT_DIR = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/EPEs/historical_mean/basins_mean'
    os.makedirs(OUT_DIR, exist_ok=True)
    for shp_path in SHP_PATHS:
        print(f'处理流域shp: {shp_path}')
        gdf = gpd.read_file(shp_path)
        result_df = pd.DataFrame()
        result_df['HYBAS_ID'] = gdf['HYBAS_ID']
        for idx in INDEX_NAMES:
            tif_path = os.path.join(TIF_DIR, f'{idx}_1971_2020_mean.tif')
            print(f'  统计指标: {idx}')
            stats = zonal_stats(
                shp_path, tif_path, stats=['mean', 'count'], nodata=None, geojson_out=False, all_touched=True
            )
            result_df[idx] = [s['mean'] for s in stats]
            result_df[f'{idx}_count'] = [s['count'] for s in stats]
        # 保存csv
        base_name = os.path.splitext(os.path.basename(shp_path))[0]
        out_csv = os.path.join(OUT_DIR, f'{base_name}_EPEs_mean.csv')
        result_df.to_csv(out_csv, index=False)
        # 保存shp
        out_shp = os.path.join(OUT_DIR, f'{base_name}_EPEs_mean.shp')
        gdf_out = gdf[['HYBAS_ID', 'geometry']].copy()  # 保留geometry
        for idx in INDEX_NAMES:
            gdf_out[idx] = result_df[idx]
            gdf_out[f'{idx}_count'] = result_df[f'{idx}_count']
        gdf_out.to_file(out_shp)
        print(f'已保存: {out_csv}, {out_shp}')
