import os
import numpy as np
import rasterio
import concurrent.futures

# 指标名
HW_indexs = ['R90pD','R90pTOT','R95pD','R95pTOT','R99pD','R99pTOT','R90pI','R95pI','R99pI','RX1-day']

# 模型名
models = [
    'ACCESS-CM2', 'ACCESS-ESM1-5', 'BCC-CSM2-MR', 'CanESM5', 'CMCC-ESM2', 
    'CNRM-CM6-1', 'CNRM-ESM2-1', 'EC-Earth3', 'EC-Earth3-Veg-LR', 'FGOALS-g3', 
    'GFDL-ESM4', 'GISS-E2-1-G', 'INM-CM4-8', 'INM-CM5-0', 'IPSL-CM6A-LR', 
    'MIROC6', 'MIROC-ES2L', 'MPI-ESM1-2-HR', 'MPI-ESM1-2-LR', 'MRI-ESM2-0',
    'NorESM2-LM', 'NorESM2-MM', 'TaiESM1'
]

# 年份
years = list(range(1971, 2015))

# 输入路径
obs_base = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/EPEs/historical'
model_base = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/EPEs/historical_models'
out_base = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/EPEs/EPEs_delta_models'

# 校正系数上下限
min_correction = 0.1
max_correction = 10.0

def process_model(model):
    print(f'处理模型: {model}')
    model_dir = os.path.join(model_base, model)
    out_dir = os.path.join(out_base, model)
    os.makedirs(out_dir, exist_ok=True)
    for idx_name in HW_indexs:
        # 1. 读取观测多年均值
        obs_years = []
        for y in years:
            obs_tif = os.path.join(obs_base, idx_name, f'{idx_name}_{y}.tif')
            with rasterio.open(obs_tif) as src:
                obs_years.append(src.read(1))
                if y == years[0]:
                    profile = src.profile
        obs_years = np.stack(obs_years, axis=0)
        obs_mean = np.nanmean(obs_years, axis=0)

        # 2. 读取模式多年均值
        model_years = []
        for y in years:
            model_tif = os.path.join(model_dir, idx_name, f'{idx_name}_{y}.tif')
            with rasterio.open(model_tif) as src:
                model_years.append(src.read(1))
        model_years = np.stack(model_years, axis=0)
        model_mean = np.nanmean(model_years, axis=0)

        # 3. 计算校正因子
        correction = np.full_like(obs_mean, np.nan, dtype=np.float32)
        # 正常情况
        mask = (model_mean > 0) & (obs_mean > 0)
        correction[mask] = obs_mean[mask] / model_mean[mask]
        # 限定上下限
        correction = np.clip(correction, min_correction, max_correction)
        # 两者都为0，校正因子为1
        both_zero = (model_mean == 0) & (obs_mean == 0)
        correction[both_zero] = 1
        # 模式为0，观测不为0，校正因子为max
        only_model_zero = (model_mean == 0) & (obs_mean > 0)
        correction[only_model_zero] = max_correction
        # 观测为0，模式不为0，校正因子为min
        only_obs_zero = (model_mean > 0) & (obs_mean == 0)
        correction[only_obs_zero] = min_correction

        # 4. 保存tif
        out_tif = os.path.join(out_dir, f'{idx_name}_correction.tif')
        profile.update(dtype=rasterio.float32, count=1, compress='lzw')
        with rasterio.open(out_tif, 'w', **profile) as dst:
            dst.write(correction, 1)
        print(f'已保存: {out_tif}')

if __name__ == '__main__':
    with concurrent.futures.ThreadPoolExecutor() as executor:
        executor.map(process_model, models)