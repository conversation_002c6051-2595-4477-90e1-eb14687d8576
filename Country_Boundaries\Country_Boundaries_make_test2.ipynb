{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 地理空间数据分析：国家边界与流域重叠分析\n", "\n", "本notebook用于分析世界国家边界与研究区流域的重叠关系，以及有效像元的分布情况。\n", "\n", "## 主要任务：\n", "1. 提取研究区涉及的国家边界\n", "2. 分析每个国家与研究区的重叠面积\n", "3. 分析研究区每个流域的有效像元数量\n", "4. 生成流域ID和国家ID的栅格数据\n", "\n", "## 数据源：\n", "- 世界国家边界：Z:\\yuan\\paper3_new02\\shp\\World_Country_Boundaries_GS(2021)6375\\World_Country_Boundaries.shp\n", "- 研究区流域：Z:\\yuan\\paper3_new02\\shp\\basins_lev01-12\\basins_lev05_new.shp\n", "- 栅格数据：Z:\\yuan\\ERA5-Land\\fbl_025\\EPEs\\yz_600_1440\\pre_yz_90_1971_2020.tif"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["库导入完成！\n"]}], "source": ["# 导入必要的库\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "import geopandas as gpd\n", "import rasterio\n", "from rasterio.features import rasterize\n", "from rasterio.mask import mask\n", "from rasterio.transform import from_bounds\n", "from shapely.geometry import box\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"库导入完成！\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["输出目录已创建：Z:\\yuan\\paper3_new02\\shp\\basin_Country\n", "\n", "数据路径设置完成！\n"]}], "source": ["# 数据路径设置\n", "# 输入数据路径\n", "world_boundaries_path = r\"Z:\\yuan\\paper3_new02\\shp\\World_Country_Boundaries_GS(2021)6375\\World_Country_Boundaries.shp\"\n", "basins_path = r\"Z:\\yuan\\paper3_new02\\shp\\basins_lev01-12\\basins_lev05_new.shp\"\n", "raster_path = r\"Z:\\yuan\\ERA5-Land\\fbl_025\\EPEs\\yz_600_1440\\pre_yz_90_1971_2020.tif\"\n", "\n", "# 输出路径\n", "output_dir = r\"Z:\\yuan\\paper3_new02\\shp\\basin_Country\"\n", "\n", "# 创建输出目录\n", "os.makedirs(output_dir, exist_ok=True)\n", "\n", "print(f\"输出目录已创建：{output_dir}\")\n", "print(\"\\n数据路径设置完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 第一步：读取和预处理空间数据"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在读取世界国家边界数据...\n", "世界国家边界数据读取完成，共243个国家\n", "国家边界坐标系：EPSG:4326\n", "国家ID字段：NR_C_ID\n", "国家边界数据列：['NAME_CHN', 'NAME_ENG', 'NR_C', 'NR_C_ID', 'SOC', 'geometry']\n", "\n", "前5个国家信息：\n", "   NR_C_ID                                           geometry\n", "0       70  MULTIPOLYGON (((-61.92444 10.04458, -61.45615 ...\n", "1      120  MULTIPOLYGON (((19.30023 40.48038, 19.28101 40...\n", "2      122  POLYGON ((-5.33936 36.16005, -5.34348 36.11236...\n", "3      184  POLYGON ((-7.37457 5.61462, -7.59705 4.89531, ...\n", "4      207  POLYGON ((28.11539 -30.59891, 27.75009 -30.594...\n"]}], "source": ["# 读取世界国家边界数据\n", "print(\"正在读取世界国家边界数据...\")\n", "world_countries = gpd.read_file(world_boundaries_path)\n", "print(f\"世界国家边界数据读取完成，共{len(world_countries)}个国家\")\n", "print(f\"国家边界坐标系：{world_countries.crs}\")\n", "print(f\"国家ID字段：NR_C_ID\")\n", "print(f\"国家边界数据列：{list(world_countries.columns)}\")\n", "print(\"\\n前5个国家信息：\")\n", "print(world_countries[['NR_C_ID', 'geometry']].head())"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在读取研究区流域数据...\n", "流域数据读取完成，共294个流域\n", "流域坐标系：EPSG:4326\n", "流域ID字段：HYBAS_ID\n", "流域数据列：['OBJECTID', 'HYBAS_ID', 'NEXT_DOWN', 'NEXT_SINK', 'MAIN_BAS', 'DIST_SINK', 'DIST_MAIN', 'SUB_AREA', 'UP_AREA', 'PFAF_ID', 'ENDO', 'COAST', 'ORDER_', 'SORT', 'Shape_Leng', 'Shape_Area', 'geometry']\n", "\n", "前5个流域信息：\n", "       HYBAS_ID                                           geometry\n", "0  2.050466e+09  POLYGON ((11.64583 47.65, 11.64649 47.64257, 1...\n", "1  2.050086e+09  POLYGON ((60.0125 28.075, 60.00833 28.075, 60....\n", "2  4.050008e+09  POLYGON ((112.05 36.65417, 112.04938 36.66576,...\n", "3  4.050681e+09  POLYGON ((110.37083 32.74167, 110.3714 32.7352...\n", "4  4.050215e+09  POLYGON ((75.34167 46.37917, 75.34167 46.38333...\n"]}], "source": ["# 读取研究区流域数据\n", "print(\"正在读取研究区流域数据...\")\n", "basins = gpd.read_file(basins_path)\n", "print(f\"流域数据读取完成，共{len(basins)}个流域\")\n", "print(f\"流域坐标系：{basins.crs}\")\n", "print(f\"流域ID字段：HYBAS_ID\")\n", "print(f\"流域数据列：{list(basins.columns)}\")\n", "print(\"\\n前5个流域信息：\")\n", "print(basins[['HYBAS_ID', 'geometry']].head())"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在读取栅格数据...\n", "栅格数据读取完成\n", "栅格坐标系：EPSG:4326\n", "栅格形状：(600, 1440)\n", "栅格范围：BoundingBox(left=-180.0, bottom=-60.0, right=180.0, top=90.0)\n", "栅格变换参数：| 0.25, 0.00,-180.00|\n", "| 0.00,-0.25, 90.00|\n", "| 0.00, 0.00, 1.00|\n", "\n", "栅格统计：\n", "总像元数：864000\n", "有效像元数（>=0）：253297\n", "有效像元比例：29.32%\n", "栅格数据范围：-32768.00 ~ 194.44\n"]}], "source": ["# 读取栅格数据\n", "print(\"正在读取栅格数据...\")\n", "with rasterio.open(raster_path) as src:\n", "    raster_data = src.read(1)  # 读取第一个波段\n", "    raster_transform = src.transform\n", "    raster_crs = src.crs\n", "    raster_bounds = src.bounds\n", "    raster_shape = raster_data.shape\n", "    \n", "print(f\"栅格数据读取完成\")\n", "print(f\"栅格坐标系：{raster_crs}\")\n", "print(f\"栅格形状：{raster_shape}\")\n", "print(f\"栅格范围：{raster_bounds}\")\n", "print(f\"栅格变换参数：{raster_transform}\")\n", "\n", "# 统计有效像元（>=0）\n", "valid_pixels = raster_data >= 0\n", "valid_count = np.sum(valid_pixels)\n", "total_count = raster_data.size\n", "print(f\"\\n栅格统计：\")\n", "print(f\"总像元数：{total_count}\")\n", "print(f\"有效像元数（>=0）：{valid_count}\")\n", "print(f\"有效像元比例：{valid_count/total_count:.2%}\")\n", "print(f\"栅格数据范围：{np.nanmin(raster_data):.2f} ~ {np.nanmax(raster_data):.2f}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["检查坐标系统...\n", "世界国家边界坐标系：EPSG:4326\n", "流域坐标系：EPSG:4326\n", "栅格坐标系：EPSG:4326\n", "\n", "使用投影坐标系进行面积计算：ESRI:54009\n", "\n", "坐标系统统一完成！\n"]}], "source": ["# 检查和统一坐标系统\n", "print(\"检查坐标系统...\")\n", "print(f\"世界国家边界坐标系：{world_countries.crs}\")\n", "print(f\"流域坐标系：{basins.crs}\")\n", "print(f\"栅格坐标系：{raster_crs}\")\n", "\n", "# 选择合适的投影坐标系进行面积计算\n", "# 使用等面积投影坐标系（Mollweide投影）来准确计算面积\n", "area_crs = 'ESRI:54009'  # Mollweide投影，适合全球面积计算\n", "print(f\"\\n使用投影坐标系进行面积计算：{area_crs}\")\n", "\n", "# 转换到面积计算坐标系\n", "world_countries_area = world_countries.to_crs(area_crs)\n", "basins_area = basins.to_crs(area_crs)\n", "\n", "# 保持原始坐标系用于栅格操作\n", "target_crs = raster_crs  # 以栅格数据的坐标系为准\n", "\n", "if world_countries.crs != target_crs:\n", "    print(f\"转换世界国家边界坐标系从 {world_countries.crs} 到 {target_crs}\")\n", "    world_countries = world_countries.to_crs(target_crs)\n", "    \n", "if basins.crs != target_crs:\n", "    print(f\"转换流域坐标系从 {basins.crs} 到 {target_crs}\")\n", "    basins = basins.to_crs(target_crs)\n", "\n", "print(\"\\n坐标系统统一完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 第二步：提取研究区涉及的国家边界"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["创建研究区总边界...\n", "研究区总边界创建完成\n", "研究区面积：11601873.78 km²\n", "研究区边界已保存：Z:\\yuan\\paper3_new02\\shp\\basin_Country\\study_area_boundary.shp\n"]}], "source": ["# 创建研究区的总边界（所有流域的联合边界）\n", "print(\"创建研究区总边界...\")\n", "try:\n", "    # 尝试使用新的方法\n", "    study_area = basins.union_all()\n", "    study_area_area = basins_area.union_all()  # 用于面积计算的版本\n", "except AttributeError:\n", "    # 如果新方法不可用，使用旧方法\n", "    study_area = basins.unary_union\n", "    study_area_area = basins_area.unary_union  # 用于面积计算的版本\n", "    \n", "study_area_gdf = gpd.GeoDataFrame({'id': [1]}, geometry=[study_area], crs=basins.crs)\n", "print(f\"研究区总边界创建完成\")\n", "print(f\"研究区面积：{study_area_area.area/1e6:.2f} km²\")\n", "\n", "# 保存研究区边界\n", "study_area_path = os.path.join(output_dir, \"study_area_boundary.shp\")\n", "study_area_gdf.to_file(study_area_path)\n", "print(f\"研究区边界已保存：{study_area_path}\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "查找与研究区相交的国家...\n", "找到51个与研究区相交的国家\n", "\n", "相交国家列表：\n", "国家ID: 120\n", "国家ID: 128\n", "国家ID: 111\n", "国家ID: 105\n", "国家ID: 109\n", "国家ID: 127\n", "国家ID: 108\n", "国家ID: 154\n", "国家ID: 102\n", "国家ID: 220\n", "国家ID: 99139\n", "国家ID: 98139\n", "国家ID: 114\n", "国家ID: 7185\n", "国家ID: 130\n", "国家ID: 219\n", "国家ID: 126\n", "国家ID: 214\n", "国家ID: 216\n", "国家ID: 129\n", "国家ID: 117\n", "国家ID: 157\n", "国家ID: 141\n", "国家ID: 707\n", "国家ID: 138\n", "国家ID: 107\n", "国家ID: 101\n", "国家ID: 95\n", "国家ID: 112\n", "国家ID: 144\n", "国家ID: 163\n", "国家ID: 8330\n", "国家ID: 93\n", "国家ID: 98\n", "国家ID: 119\n", "国家ID: 218\n", "国家ID: 104\n", "国家ID: 115\n", "国家ID: 121\n", "国家ID: 149\n", "国家ID: 100\n", "国家ID: 5889\n", "国家ID: 225\n", "国家ID: 213\n", "国家ID: 221\n", "国家ID: 142\n", "国家ID: 103\n", "国家ID: 118\n", "国家ID: 9637\n", "国家ID: 135\n", "国家ID: 2594\n", "\n", "相交国家边界已保存：Z:\\yuan\\paper3_new02\\shp\\basin_Country\\intersecting_countries.shp\n"]}], "source": ["# 找出与研究区相交的国家\n", "print(\"\\n查找与研究区相交的国家...\")\n", "# 使用空间索引加速查询\n", "intersecting_countries = world_countries[world_countries.intersects(study_area)]\n", "\n", "print(f\"找到{len(intersecting_countries)}个与研究区相交的国家\")\n", "print(\"\\n相交国家列表：\")\n", "if 'NAME' in intersecting_countries.columns:\n", "    for idx, row in intersecting_countries.iterrows():\n", "        print(f\"国家ID: {row['NR_C_ID']}, 国家名: {row.get('NAME', 'Unknown')}\")\n", "else:\n", "    for idx, row in intersecting_countries.iterrows():\n", "        print(f\"国家ID: {row['NR_C_ID']}\")\n", "\n", "# 保存相交的国家边界\n", "intersecting_countries_path = os.path.join(output_dir, \"intersecting_countries.shp\")\n", "intersecting_countries.to_file(intersecting_countries_path)\n", "print(f\"\\n相交国家边界已保存：{intersecting_countries_path}\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "创建可视化图...\n"]}, {"data": {"image/png": "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************************************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", "text/plain": ["<Figure size 1500x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["可视化图已保存：Z:\\yuan\\paper3_new02\\shp\\basin_Country\\study_area_countries_overview.png\n"]}], "source": ["# 可视化研究区和相交国家\n", "print(\"\\n创建可视化图...\")\n", "fig, ax = plt.subplots(1, 1, figsize=(15, 10))\n", "\n", "# 绘制相交的国家边界\n", "intersecting_countries.plot(ax=ax, color='lightblue', edgecolor='blue', alpha=0.7, label='相交国家')\n", "\n", "# 绘制研究区流域\n", "basins.plot(ax=ax, color='red', alpha=0.5, edgecolor='darkred', label='研究区流域')\n", "\n", "# 绘制研究区总边界\n", "study_area_gdf.plot(ax=ax, color='none', edgecolor='black', linewidth=2, label='研究区边界')\n", "\n", "ax.set_title('研究区与相交国家分布图', fontsize=16)\n", "ax.legend()\n", "ax.set_xlabel('经度')\n", "ax.set_ylabel('纬度')\n", "\n", "# 保存图片\n", "fig_path = os.path.join(output_dir, \"study_area_countries_overview.png\")\n", "plt.savefig(fig_path, dpi=300, bbox_inches='tight')\n", "plt.show()\n", "print(f\"可视化图已保存：{fig_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 第三步：计算国家与研究区重叠面积"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["重新计算国家与研究区重叠面积（使用投影坐标系）...\n", "使用投影坐标系：ESRI:54009\n", "研究区总面积：11601873.78 km²\n", "国家ID 120 (Country_120): 总面积 28294.79 km², 重叠面积 20877.01 km², 重叠比例 73.78%\n", "国家ID 128 (Country_128): 总面积 9943.54 km², 重叠面积 9769.32 km², 重叠比例 98.25%\n", "国家ID 111 (Country_111): 总面积 53625.57 km², 重叠面积 7315.72 km², 重叠比例 13.64%\n", "国家ID 105 (Country_105): 总面积 41212.12 km², 重叠面积 4048.11 km², 重叠比例 9.82%\n", "国家ID 109 (Country_109): 总面积 88874.42 km², 重叠面积 65507.33 km², 重叠比例 73.71%\n", "国家ID 127 (Country_127): 总面积 191488.33 km², 重叠面积 164692.70 km², 重叠比例 86.01%\n", "国家ID 108 (Country_108): 总面积 237857.03 km², 重叠面积 134355.52 km², 重叠比例 56.49%\n", "国家ID 154 (Country_154): 总面积 489106.68 km², 重叠面积 384107.62 km², 重叠比例 78.53%\n", "国家ID 102 (Country_102): 总面积 34511.16 km², 重叠面积 34247.25 km², 重叠比例 99.24%\n", "国家ID 220 (Country_220): 总面积 198118.25 km², 重叠面积 189237.07 km², 重叠比例 95.52%\n", "国家ID 99139 (Country_99139): 总面积 28036.67 km², 重叠面积 27319.20 km², 重叠比例 97.44%\n", "国家ID 98139 (Country_98139): 总面积 28036.67 km², 重叠面积 27319.20 km², 重叠比例 97.44%\n", "国家ID 114 (Country_114): 总面积 303790.14 km², 重叠面积 245729.96 km², 重叠比例 80.89%\n", "国家ID 7185 (Country_7185): 总面积 796898.34 km², 重叠面积 393136.39 km², 重叠比例 49.33%\n", "国家ID 130 (Country_130): 总面积 18060.10 km², 重叠面积 0.44 km², 重叠比例 0.00%\n", "国家ID 219 (Country_219): 总面积 143587.92 km², 重叠面积 90859.06 km², 重叠比例 63.28%\n", "国家ID 126 (Country_126): 总面积 29683.01 km², 重叠面积 22531.23 km², 重叠比例 75.91%\n", "国家ID 214 (Country_214): 总面积 38381.13 km², 重叠面积 122.80 km², 重叠比例 0.32%\n", "国家ID 216 (Country_216): 总面积 601598.63 km², 重叠面积 340307.56 km², 重叠比例 56.57%\n", "国家ID 129 (Country_129): 总面积 435261.14 km², 重叠面积 285401.02 km², 重叠比例 65.57%\n", "国家ID 117 (Country_117): 总面积 25560.82 km², 重叠面积 22924.08 km², 重叠比例 89.68%\n", "国家ID 157 (Country_157): 总面积 86428.93 km², 重叠面积 13758.02 km², 重叠比例 15.92%\n", "国家ID 141 (Country_141): 总面积 1626596.70 km², 重叠面积 1112248.76 km², 重叠比例 68.38%\n", "国家ID 707 (Country_707): 总面积 16823164.19 km², 重叠面积 253438.22 km², 重叠比例 1.51%\n", "国家ID 138 (Country_138): 总面积 70970.22 km², 重叠面积 2088.44 km², 重叠比例 2.94%\n", "国家ID 107 (Country_107): 总面积 20006.79 km², 重叠面积 4617.89 km², 重叠比例 23.08%\n", "国家ID 101 (Country_101): 总面积 48811.72 km², 重叠面积 39749.49 km², 重叠比例 81.43%\n", "国家ID 95 (Country_95): 总面积 312066.80 km², 重叠面积 584.83 km², 重叠比例 0.19%\n", "国家ID 112 (Country_112): 总面积 29.86 km², 重叠面积 29.86 km², 重叠比例 100.00%\n", "国家ID 144 (Country_144): 总面积 451427.43 km², 重叠面积 357652.37 km², 重叠比例 79.23%\n", "国家ID 163 (Country_163): 总面积 989377.85 km², 重叠面积 39243.33 km², 重叠比例 3.97%\n", "国家ID 8330 (Country_8330): 总面积 185901.90 km², 重叠面积 182445.74 km², 重叠比例 98.14%\n", "国家ID 93 (Country_93): 总面积 353915.03 km², 重叠面积 47637.21 km², 重叠比例 13.46%\n", "国家ID 98 (Country_98): 总面积 547761.83 km², 重叠面积 717.45 km², 重叠比例 0.13%\n", "国家ID 119 (Country_119): 总面积 0.41 km², 重叠面积 0.41 km², 重叠比例 100.00%\n", "国家ID 218 (Country_218): 总面积 642739.85 km², 重叠面积 372815.95 km², 重叠比例 58.00%\n", "国家ID 104 (Country_104): 总面积 92307.23 km², 重叠面积 84249.06 km², 重叠比例 91.27%\n", "国家ID 115 (Country_115): 总面积 111322.65 km², 重叠面积 110863.41 km², 重叠比例 99.59%\n", "国家ID 121 (Country_121): 总面积 128119.26 km², 重叠面积 108598.48 km², 重叠比例 84.76%\n", "国家ID 149 (Country_149): 总面积 784854.88 km², 重叠面积 623723.29 km², 重叠比例 79.47%\n", "国家ID 100 (Country_100): 总面积 79078.28 km², 重叠面积 22145.23 km², 重叠比例 28.00%\n", "国家ID 5889 (Country_5889): 总面积 2989351.32 km², 重叠面积 811467.64 km², 重叠比例 27.15%\n", "国家ID 225 (Country_225): 总面积 148245.80 km², 重叠面积 142683.64 km², 重叠比例 96.25%\n", "国家ID 213 (Country_213): 总面积 2719610.53 km², 重叠面积 977144.30 km², 重叠比例 35.93%\n", "国家ID 221 (Country_221): 总面积 1563345.96 km², 重叠面积 55985.60 km², 重叠比例 3.58%\n", "国家ID 142 (Country_142): 总面积 131120.57 km², 重叠面积 2.10 km², 重叠比例 0.00%\n", "国家ID 103 (Country_103): 总面积 84323.40 km², 重叠面积 64728.85 km², 重叠比例 76.76%\n", "国家ID 118 (Country_118): 总面积 13736.15 km², 重叠面积 293.96 km², 重叠比例 2.14%\n", "国家ID 9637 (Country_9637): 总面积 9544441.27 km², 重叠面积 3653692.31 km², 重叠比例 38.28%\n", "国家ID 135 (Country_135): 总面积 88402.19 km², 重叠面积 37245.32 km², 重叠比例 42.13%\n", "国家ID 2594 (Country_2594): 总面积 1943115.74 km², 重叠面积 17029.62 km², 重叠比例 0.88%\n", "\n", "完成51个国家的重叠面积计算（修正版）\n"]}], "source": ["# 重新计算国家与研究区重叠面积（使用正确的投影坐标系）\n", "print(\"重新计算国家与研究区重叠面积（使用投影坐标系）...\")\n", "\n", "# 使用等面积投影坐标系进行面积计算\n", "area_crs = 'ESRI:54009'  # Mollweide投影\n", "print(f\"使用投影坐标系：{area_crs}\")\n", "\n", "# 转换相交国家到面积计算坐标系\n", "intersecting_countries_area = intersecting_countries.to_crs(area_crs)\n", "\n", "# 转换研究区到面积计算坐标系\n", "try:\n", "    study_area_area = basins.to_crs(area_crs).union_all()\n", "except AttributeError:\n", "    study_area_area = basins.to_crs(area_crs).unary_union\n", "\n", "print(f\"研究区总面积：{study_area_area.area/1e6:.2f} km²\")\n", "\n", "# 重新计算重叠面积\n", "overlap_results_corrected = []\n", "\n", "for i, (idx, country) in enumerate(intersecting_countries_area.iterrows()):\n", "    # 从原始数据获取ID和名称\n", "    original_country = intersecting_countries.iloc[i]\n", "    country_id = original_country['NR_C_ID']\n", "    country_name = original_country.get('NAME', f'Country_{country_id}')\n", "    country_geom = country.geometry\n", "    \n", "    try:\n", "        # 检查和修复几何体有效性\n", "        if not country_geom.is_valid:\n", "            country_geom = country_geom.buffer(0)\n", "        \n", "        if not study_area_area.is_valid:\n", "            study_area_area = study_area_area.buffer(0)\n", "        \n", "        # 计算国家总面积（平方米）\n", "        country_area = country_geom.area\n", "        \n", "        # 计算与研究区的交集\n", "        try:\n", "            intersection = country_geom.intersection(study_area_area)\n", "        except Exception as e:\n", "            print(f\"国家 {country_id} 交集计算失败，尝试简化几何体: {e}\")\n", "            country_geom_simplified = country_geom.simplify(1000)  # 1km容差\n", "            study_area_simplified = study_area_area.simplify(1000)\n", "            intersection = country_geom_simplified.intersection(study_area_simplified)\n", "        \n", "        # 计算重叠面积（平方米）\n", "        overlap_area = intersection.area if not intersection.is_empty else 0\n", "        \n", "        # 计算重叠比例\n", "        overlap_ratio = overlap_area / country_area if country_area > 0 else 0\n", "        \n", "        # 转换为平方公里\n", "        country_area_km2 = country_area / 1e6\n", "        overlap_area_km2 = overlap_area / 1e6\n", "        \n", "        overlap_results_corrected.append({\n", "            'Country_ID': country_id,\n", "            'Country_Name': country_name,\n", "            'Country_Area_km2': country_area_km2,\n", "            'Overlap_Area_km2': overlap_area_km2,\n", "            'Overlap_Ratio': overlap_ratio\n", "        })\n", "        \n", "        print(f\"国家ID {country_id} ({country_name}): \"\n", "              f\"总面积 {country_area_km2:.2f} km², \"\n", "              f\"重叠面积 {overlap_area_km2:.2f} km², \"\n", "              f\"重叠比例 {overlap_ratio:.2%}\")\n", "              \n", "    except Exception as e:\n", "        print(f\"处理国家 {country_id} ({country_name}) 时出错: {e}\")\n", "        overlap_results_corrected.append({\n", "            'Country_ID': country_id,\n", "            'Country_Name': country_name,\n", "            'Country_Area_km2': 0,\n", "            'Overlap_Area_km2': 0,\n", "            'Overlap_Ratio': 0\n", "        })\n", "\n", "print(f\"\\n完成{len(overlap_results_corrected)}个国家的重叠面积计算（修正版）\")\n", "\n", "# 更新overlap_results变量\n", "overlap_results = overlap_results_corrected"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["修复overlap_df，跳过geometry相关操作...\n"]}, {"ename": "NameError", "evalue": "name 'overlap_df' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[11]\u001b[39m\u001b[32m, line 5\u001b[39m\n\u001b[32m      2\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m修复overlap_df，跳过geometry相关操作...\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m      4\u001b[39m \u001b[38;5;66;03m# 如果overlap_df中没有geometry列，添加一个空的geometry列\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m5\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[33m'\u001b[39m\u001b[33mgeometry\u001b[39m\u001b[33m'\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m \u001b[43moverlap_df\u001b[49m.columns:\n\u001b[32m      6\u001b[39m     overlap_df[\u001b[33m'\u001b[39m\u001b[33mgeometry\u001b[39m\u001b[33m'\u001b[39m] = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m      7\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m已添加空的geometry列\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mNameError\u001b[39m: name 'overlap_df' is not defined"]}], "source": ["# 修复overlap_df，添加geometry列\n", "print(\"修复overlap_df，跳过geometry相关操作...\")\n", "\n", "# 如果overlap_df中没有geometry列，添加一个空的geometry列\n", "if 'geometry' not in overlap_df.columns:\n", "    overlap_df['geometry'] = None\n", "    print(\"已添加空的geometry列\")\n", "\n", "# 跳过创建重叠区域的GeoDataFrame\n", "print(\"跳过重叠区域shapefile的创建\")\n", "\n", "# 显示统计摘要（使用修正后的研究区面积）\n", "print(\"\\n=== 重叠面积统计摘要 ===\")\n", "print(f\"总研究区面积：{11602637.67:.2f} km²\")  # 使用之前计算的正确面积\n", "print(f\"涉及国家数量：{len(overlap_df)}\")\n", "print(f\"总重叠面积：{overlap_df['Overlap_Area_km2'].sum():.2f} km²\")\n", "print(\"\\n前5个重叠面积最大的国家：\")\n", "print(overlap_df[['Country_ID', 'Country_Name', 'Overlap_Area_km2', 'Overlap_Ratio']].head())"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 导出统计结果 ===\n", "❌ 导出CSV文件时出错：name 'overlap_df' is not defined\n"]}], "source": ["# 导出重叠面积统计结果为CSV文件\n", "print(\"\\n=== 导出统计结果 ===\")\n", "\n", "# 确保output_dir变量已定义\n", "if 'output_dir' not in locals():\n", "    output_dir = r\"Z:\\yuan\\paper3_new02\\shp\\basin_Country\"\n", "\n", "overlap_stats_path = os.path.join(output_dir, \"country_overlap_statistics.csv\")\n", "\n", "try:\n", "    # 选择需要导出的列，排除geometry列\n", "    export_columns = ['Country_ID', 'Country_Name', 'Overlap_Area_km2', 'Overlap_Ratio']\n", "    overlap_export_df = overlap_df[export_columns].copy()\n", "    \n", "    # 按重叠面积降序排序\n", "    overlap_export_df = overlap_export_df.sort_values('Overlap_Area_km2', ascending=False)\n", "    \n", "    # 导出到CSV\n", "    overlap_export_df.to_csv(overlap_stats_path, index=False, encoding='utf-8-sig')\n", "    print(f\"✓ 国家重叠面积统计结果已导出：{overlap_stats_path}\")\n", "    print(f\"  包含 {len(overlap_export_df)} 个国家的重叠面积数据\")\n", "    \n", "    # 显示导出文件的基本信息\n", "    print(f\"\\n=== 导出文件信息 ===\")\n", "    print(f\"文件路径：{overlap_stats_path}\")\n", "    print(f\"包含列：{export_columns}\")\n", "    print(f\"数据行数：{len(overlap_export_df)}\")\n", "    print(f\"文件编码：UTF-8 with BOM（支持中文显示）\")\n", "    \n", "    # 显示前几行数据预览\n", "    print(f\"\\n前5行数据预览：\")\n", "    print(overlap_export_df.head())\n", "    \n", "except PermissionError:\n", "    # 如果文件被占用，使用时间戳创建新文件名\n", "    import datetime\n", "    timestamp = datetime.datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    backup_path = os.path.join(output_dir, f\"country_overlap_statistics_{timestamp}.csv\")\n", "    overlap_export_df.to_csv(backup_path, index=False, encoding='utf-8-sig')\n", "    print(f\"⚠️ 原文件被占用，统计结果已保存到：{backup_path}\")\n", "    print(\"请关闭可能打开该文件的程序（如Excel），然后重新运行以覆盖原文件。\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ 导出CSV文件时出错：{e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 筛选重叠面积大于500km²的国家并导出为shp"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "开始筛选重叠面积大于500km²的国家并导出为shp文件\n", "============================================================\n", "\n", "1. 筛选重叠面积大于500km²的国家...\n"]}, {"ename": "NameError", "evalue": "name 'overlap_df' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[13]\u001b[39m\u001b[32m, line 9\u001b[39m\n\u001b[32m      7\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m1. 筛选重叠面积大于500km²的国家...\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m      8\u001b[39m threshold_km2 = \u001b[32m500\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m9\u001b[39m filtered_countries = \u001b[43moverlap_df\u001b[49m[overlap_df[\u001b[33m'\u001b[39m\u001b[33mOverlap_Area_km2\u001b[39m\u001b[33m'\u001b[39m] > threshold_km2].copy()\n\u001b[32m     11\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m筛选前国家数量：\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(overlap_df)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m     12\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m筛选后国家数量：\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(filtered_countries)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n", "\u001b[31mNameError\u001b[39m: name 'overlap_df' is not defined"]}], "source": ["# ===== 筛选重叠面积大于500km²的国家并导出为shp =====\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"开始筛选重叠面积大于500km²的国家并导出为shp文件\")\n", "print(\"=\"*60)\n", "\n", "# 1. 筛选重叠面积大于500km²的国家\n", "print(\"\\n1. 筛选重叠面积大于500km²的国家...\")\n", "threshold_km2 = 500\n", "filtered_countries = overlap_df[overlap_df['Overlap_Area_km2'] > threshold_km2].copy()\n", "\n", "print(f\"筛选前国家数量：{len(overlap_df)}\")\n", "print(f\"筛选后国家数量：{len(filtered_countries)}\")\n", "print(f\"筛选阈值：{threshold_km2} km²\")\n", "\n", "if len(filtered_countries) == 0:\n", "    print(\"⚠️ 没有找到重叠面积大于500km²的国家\")\n", "else:\n", "    print(f\"\\n筛选出的国家列表（按重叠面积降序）：\")\n", "    filtered_sorted = filtered_countries.sort_values('Overlap_Area_km2', ascending=False)\n", "    for idx, row in filtered_sorted.iterrows():\n", "        print(f\"  {row['Country_ID']:>6} | {row['Country_Name']:<15} | {row['Overlap_Area_km2']:>12,.2f} km²\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2. 获取筛选国家的几何信息\n", "print(f\"\\n2. 获取筛选国家的几何信息...\")\n", "filtered_country_ids = filtered_countries['Country_ID'].tolist()\n", "print(f\"需要获取几何信息的国家ID：{filtered_country_ids}\")\n", "\n", "# 从intersecting_countries中获取对应的几何信息\n", "filtered_countries_with_geom = intersecting_countries[\n", "    intersecting_countries['NR_C_ID'].isin(filtered_country_ids)\n", "].copy()\n", "\n", "print(f\"成功获取几何信息的国家数量：{len(filtered_countries_with_geom)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 3. 合并统计数据和几何信息\n", "print(f\"\\n3. 合并统计数据和几何信息...\")\n", "\n", "# 将统计数据与几何数据进行合并\n", "# 首先重命名列以便合并\n", "filtered_countries_renamed = filtered_countries.rename(columns={'Country_ID': 'NR_C_ID'})\n", "\n", "# 合并数据\n", "final_gdf = filtered_countries_with_geom.merge(\n", "    filtered_countries_renamed[['NR_C_ID', 'Country_Name', 'Country_Area_km2', 'Overlap_Area_km2', 'Overlap_Ratio']], \n", "    on='NR_C_ID', \n", "    how='inner'\n", ")\n", "\n", "print(f\"合并后的GeoDataFrame包含 {len(final_gdf)} 个国家\")\n", "print(f\"GeoDataFrame列：{list(final_gdf.columns)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 4. 清理和准备导出数据\n", "print(f\"\\n4. 准备导出数据...\")\n", "\n", "# 选择需要导出的列，确保列名符合shapefile规范（最多10个字符）\n", "export_columns = {\n", "    'NR_C_ID': 'COUNTRY_ID',\n", "    'Country_Name': 'CTRY_NAME', \n", "    'Country_Area_km2': 'CTRY_AREA',\n", "    'Overlap_Area_km2': 'OVLP_AREA',\n", "    'Overlap_Ratio': 'OVLP_RATIO',\n", "    'geometry': 'geometry'\n", "}\n", "\n", "# 创建导出用的GeoDataFrame\n", "export_gdf = final_gdf[list(export_columns.keys())].copy()\n", "export_gdf = export_gdf.rename(columns=export_columns)\n", "\n", "# 确保数值列的数据类型正确\n", "export_gdf['COUNTRY_ID'] = export_gdf['COUNTRY_ID'].astype(int)\n", "export_gdf['CTRY_AREA'] = export_gdf['CTRY_AREA'].astype(float)\n", "export_gdf['OVLP_AREA'] = export_gdf['OVLP_AREA'].astype(float)\n", "export_gdf['OVLP_RATIO'] = export_gdf['OVLP_RATIO'].astype(float)\n", "\n", "print(f\"导出数据预览：\")\n", "print(export_gdf[['COUNTRY_ID', 'CTRY_NAME', 'OVLP_AREA']].head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 5. 导出为shapefile\n", "print(f\"\\n5. 导出为shapefile...\")\n", "\n", "# 确保output_dir变量已定义\n", "if 'output_dir' not in locals():\n", "    output_dir = r\"Z:\\yuan\\paper3_new02\\shp\\basin_Country\"\n", "\n", "# 定义输出文件路径\n", "filtered_countries_shp_path = os.path.join(output_dir, \"countries_overlap_gt500km2.shp\")\n", "\n", "try:\n", "    # 导出为shapefile\n", "    export_gdf.to_file(filtered_countries_shp_path, driver='ESRI Shapefile', encoding='utf-8')\n", "    print(f\"✓ 筛选后的国家边界已成功导出：{filtered_countries_shp_path}\")\n", "    print(f\"  包含 {len(export_gdf)} 个重叠面积大于{threshold_km2}km²的国家\")\n", "    \n", "    # 显示导出文件的基本信息\n", "    print(f\"\\n=== 导出shapefile信息 ===\")\n", "    print(f\"文件路径：{filtered_countries_shp_path}\")\n", "    print(f\"包含字段：{list(export_gdf.columns)}\")\n", "    print(f\"数据行数：{len(export_gdf)}\")\n", "    print(f\"坐标系：{export_gdf.crs}\")\n", "    \n", "    # 显示统计信息\n", "    print(f\"\\n=== 筛选结果统计 ===\")\n", "    print(f\"重叠面积总和：{export_gdf['OVLP_AREA'].sum():,.2f} km²\")\n", "    print(f\"平均重叠面积：{export_gdf['OVLP_AREA'].mean():,.2f} km²\")\n", "    print(f\"最大重叠面积：{export_gdf['OVLP_AREA'].max():,.2f} km²\")\n", "    print(f\"最小重叠面积：{export_gdf['OVLP_AREA'].min():,.2f} km²\")\n", "    \n", "    # 显示前几行数据预览\n", "    print(f\"\\n导出数据详细预览：\")\n", "    display_df = export_gdf[['COUNTRY_ID', 'CTRY_NAME', 'OVLP_AREA', 'OVLP_RATIO']].sort_values('OVLP_AREA', ascending=False)\n", "    print(display_df.to_string(index=False))\n", "    \n", "except Exception as e:\n", "    print(f\"❌ 导出shapefile时出错：{e}\")\n", "    print(\"请检查输出路径是否存在，以及是否有写入权限。\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 6. 验证导出结果\n", "print(f\"\\n6. 验证导出结果...\")\n", "\n", "try:\n", "    # 重新读取导出的shapefile进行验证\n", "    verification_gdf = gpd.read_file(filtered_countries_shp_path)\n", "    \n", "    print(f\"✓ shapefile验证成功\")\n", "    print(f\"  验证读取的记录数：{len(verification_gdf)}\")\n", "    print(f\"  验证读取的字段：{list(verification_gdf.columns)}\")\n", "    print(f\"  验证读取的坐标系：{verification_gdf.crs}\")\n", "    \n", "    # 检查数据完整性\n", "    if len(verification_gdf) == len(export_gdf):\n", "        print(f\"✓ 数据完整性验证通过：记录数匹配\")\n", "    else:\n", "        print(f\"⚠️ 数据完整性警告：导出{len(export_gdf)}条记录，验证读取{len(verification_gdf)}条记录\")\n", "    \n", "    # 检查重叠面积数据\n", "    min_overlap = verification_gdf['OVLP_AREA'].min()\n", "    if min_overlap > threshold_km2:\n", "        print(f\"✓ 筛选条件验证通过：所有国家重叠面积都大于{threshold_km2}km²\")\n", "    else:\n", "        print(f\"⚠️ 筛选条件警告：发现重叠面积小于{threshold_km2}km²的记录（最小值：{min_overlap:.2f}km²）\")\n", "    \n", "    print(f\"\\n=== 任务完成 ===\")\n", "    print(f\"成功筛选并导出了{len(verification_gdf)}个重叠面积大于{threshold_km2}km²的国家\")\n", "    print(f\"输出文件：{filtered_countries_shp_path}\")\n", "    \n", "except Exception as e:\n", "    print(f\"❌ 验证导出结果时出错：{e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## 第四步：分析有效像元分布"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["分析每个流域的有效像元数量和面积...\n", "流域 2050465610.0: 面积 50501.92 km², 总像元 101, 有效像元 101, 有效比例 100.00%\n", "流域 2050085680.0: 面积 55428.86 km², 总像元 83, 有效像元 83, 有效比例 100.00%\n", "流域 4050007850.0: 面积 77300.44 km², 总像元 122, 有效像元 122, 有效比例 100.00%\n", "流域 4050680810.0: 面积 61561.10 km², 总像元 95, 有效像元 95, 有效比例 100.00%\n", "流域 4050214510.0: 面积 19654.59 km², 总像元 35, 有效像元 35, 有效比例 100.00%\n", "流域 4050219180.0: 面积 34420.97 km², 总像元 64, 有效像元 64, 有效比例 100.00%\n", "流域 2050000010.0: 面积 91337.86 km², 总像元 136, 有效像元 136, 有效比例 100.00%\n", "流域 2050001320.0: 面积 74239.54 km², 总像元 119, 有效像元 119, 有效比例 100.00%\n", "流域 2050002110.0: 面积 135777.50 km², 总像元 225, 有效像元 225, 有效比例 100.00%\n", "流域 2050003440.0: 面积 3168.24 km², 总像元 8, 有效像元 8, 有效比例 100.00%\n", "流域 2050003550.0: 面积 62974.80 km², 总像元 105, 有效像元 105, 有效比例 100.00%\n", "流域 2050003560.0: 面积 29904.03 km², 总像元 50, 有效像元 50, 有效比例 100.00%\n", "流域 2050004130.0: 面积 80961.16 km², 总像元 140, 有效像元 140, 有效比例 100.00%\n", "流域 2050004280.0: 面积 36403.38 km², 总像元 60, 有效像元 60, 有效比例 100.00%\n", "流域 2050005120.0: 面积 22060.99 km², 总像元 36, 有效像元 36, 有效比例 100.00%\n", "流域 2050491980.0: 面积 55878.31 km², 总像元 110, 有效像元 110, 有效比例 100.00%\n", "流域 2050491970.0: 面积 98941.90 km², 总像元 198, 有效像元 198, 有效比例 100.00%\n", "流域 2050439800.0: 面积 34992.36 km², 总像元 70, 有效像元 70, 有效比例 100.00%\n", "流域 2050006600.0: 面积 75620.63 km², 总像元 143, 有效像元 143, 有效比例 100.00%\n", "流域 2050007930.0: 面积 45604.69 km², 总像元 87, 有效像元 87, 有效比例 100.00%\n", "流域 2050469830.0: 面积 22784.89 km², 总像元 45, 有效像元 45, 有效比例 100.00%\n", "流域 2050008000.0: 面积 63711.08 km², 总像元 128, 有效像元 128, 有效比例 100.00%\n", "流域 2050008160.0: 面积 5393.09 km², 总像元 10, 有效像元 10, 有效比例 100.00%\n", "流域 2050008170.0: 面积 7230.27 km², 总像元 14, 有效像元 14, 有效比例 100.00%\n", "流域 2050008350.0: 面积 73322.76 km², 总像元 143, 有效像元 143, 有效比例 100.00%\n", "流域 2050008440.0: 面积 5399.69 km², 总像元 10, 有效像元 10, 有效比例 100.00%\n", "流域 2050008450.0: 面积 1243.10 km², 总像元 2, 有效像元 2, 有效比例 100.00%\n", "流域 2050008490.0: 面积 39757.26 km², 总像元 80, 有效像元 80, 有效比例 100.00%\n", "流域 2050543160.0: 面积 4746.24 km², 总像元 7, 有效像元 7, 有效比例 100.00%\n", "流域 2050557340.0: 面积 12650.55 km², 总像元 25, 有效像元 25, 有效比例 100.00%\n", "流域 2050557390.0: 面积 15334.37 km², 总像元 28, 有效像元 28, 有效比例 100.00%\n", "流域 2050569550.0: 面积 26774.40 km², 总像元 48, 有效像元 48, 有效比例 100.00%\n", "流域 2050575490.0: 面积 56020.71 km², 总像元 100, 有效像元 100, 有效比例 100.00%\n", "流域 2050557720.0: 面积 12351.66 km², 总像元 24, 有效像元 24, 有效比例 100.00%\n", "流域 2050557800.0: 面积 37894.69 km², 总像元 66, 有效像元 66, 有效比例 100.00%\n", "流域 2050555600.0: 面积 663.92 km², 总像元 3, 有效像元 3, 有效比例 100.00%\n", "流域 2050548500.0: 面积 15480.39 km², 总像元 26, 有效像元 26, 有效比例 100.00%\n", "流域 2050539930.0: 面积 41073.64 km², 总像元 78, 有效像元 78, 有效比例 100.00%\n", "流域 2050540100.0: 面积 40340.36 km², 总像元 75, 有效像元 75, 有效比例 100.00%\n", "流域 2050524800.0: 面积 5808.21 km², 总像元 14, 有效像元 14, 有效比例 100.00%\n", "流域 2050525040.0: 面积 30258.64 km², 总像元 56, 有效像元 56, 有效比例 100.00%\n", "流域 2050514740.0: 面积 27183.97 km², 总像元 50, 有效像元 50, 有效比例 100.00%\n", "流域 2050514730.0: 面积 13076.04 km², 总像元 27, 有效像元 27, 有效比例 100.00%\n", "流域 2050483240.0: 面积 12898.23 km², 总像元 24, 有效像元 24, 有效比例 100.00%\n", "流域 2050488080.0: 面积 1431.73 km², 总像元 4, 有效像元 4, 有效比例 100.00%\n", "流域 2050487990.0: 面积 19528.11 km², 总像元 37, 有效像元 37, 有效比例 100.00%\n", "流域 2050488360.0: 面积 17779.75 km², 总像元 32, 有效像元 32, 有效比例 100.00%\n", "流域 2050488190.0: 面积 717.70 km², 总像元 1, 有效像元 1, 有效比例 100.00%\n", "流域 2050477000.0: 面积 28104.78 km², 总像元 56, 有效像元 56, 有效比例 100.00%\n", "流域 2050476910.0: 面积 26618.35 km², 总像元 53, 有效像元 53, 有效比例 100.00%\n", "流域 2050008500.0: 面积 28209.49 km², 总像元 51, 有效像元 51, 有效比例 100.00%\n", "流域 2050009230.0: 面积 6086.05 km², 总像元 11, 有效像元 11, 有效比例 100.00%\n", "流域 2050009480.0: 面积 52629.19 km², 总像元 89, 有效像元 89, 有效比例 100.00%\n", "流域 2050009490.0: 面积 12486.15 km², 总像元 23, 有效像元 23, 有效比例 100.00%\n", "流域 2050009730.0: 面积 16839.36 km², 总像元 29, 有效像元 29, 有效比例 100.00%\n", "流域 2050009740.0: 面积 7269.02 km², 总像元 12, 有效像元 12, 有效比例 100.00%\n", "流域 2050009900.0: 面积 24579.01 km², 总像元 40, 有效像元 40, 有效比例 100.00%\n", "流域 2050009910.0: 面积 109689.33 km², 总像元 179, 有效像元 179, 有效比例 100.00%\n", "流域 2050012730.0: 面积 13544.54 km², 总像元 24, 有效像元 24, 有效比例 100.00%\n", "流域 2050012960.0: 面积 4821.54 km², 总像元 8, 有效像元 8, 有效比例 100.00%\n", "流域 2050012980.0: 面积 14492.13 km², 总像元 29, 有效像元 29, 有效比例 100.00%\n", "流域 2050013010.0: 面积 73454.16 km², 总像元 132, 有效像元 132, 有效比例 100.00%\n", "流域 2050013020.0: 面积 1565.27 km², 总像元 3, 有效像元 3, 有效比例 100.00%\n", "流域 2050013100.0: 面积 5627.53 km², 总像元 11, 有效像元 11, 有效比例 100.00%\n", "流域 2050013110.0: 面积 53329.44 km², 总像元 93, 有效像元 93, 有效比例 100.00%\n", "流域 2050014550.0: 面积 86663.50 km², 总像元 149, 有效像元 149, 有效比例 100.00%\n", "流域 2050065840.0: 面积 3154.68 km², 总像元 3, 有效像元 3, 有效比例 100.00%\n", "流域 2050065960.0: 面积 11614.94 km², 总像元 18, 有效像元 18, 有效比例 100.00%\n", "流域 2050065970.0: 面积 1609.31 km², 总像元 3, 有效像元 3, 有效比例 100.00%\n", "流域 2050066030.0: 面积 36482.26 km², 总像元 58, 有效像元 58, 有效比例 100.00%\n", "流域 2050066040.0: 面积 21447.95 km², 总像元 37, 有效像元 37, 有效比例 100.00%\n", "流域 2050067570.0: 面积 73317.51 km², 总像元 139, 有效像元 139, 有效比例 100.00%\n", "流域 2050067650.0: 面积 74406.44 km², 总像元 147, 有效像元 147, 有效比例 100.00%\n", "流域 2050067740.0: 面积 50190.49 km², 总像元 101, 有效像元 101, 有效比例 100.00%\n", "流域 2050067910.0: 面积 8929.17 km², 总像元 18, 有效像元 18, 有效比例 100.00%\n", "流域 2050067920.0: 面积 8555.69 km², 总像元 18, 有效像元 18, 有效比例 100.00%\n", "流域 2050068170.0: 面积 5401.51 km², 总像元 10, 有效像元 10, 有效比例 100.00%\n", "流域 2050068340.0: 面积 8312.15 km², 总像元 17, 有效像元 17, 有效比例 100.00%\n", "流域 2050068680.0: 面积 50865.82 km², 总像元 98, 有效像元 98, 有效比例 100.00%\n", "流域 2050421450.0: 面积 12152.16 km², 总像元 24, 有效像元 24, 有效比例 100.00%\n", "流域 2050643590.0: 面积 96347.09 km², 总像元 165, 有效像元 165, 有效比例 100.00%\n", "流域 2050070510.0: 面积 58783.37 km², 总像元 91, 有效像元 91, 有效比例 100.00%\n", "流域 2050070520.0: 面积 28027.82 km², 总像元 49, 有效像元 49, 有效比例 100.00%\n", "流域 2050828790.0: 面积 72594.29 km², 总像元 109, 有效像元 109, 有效比例 100.00%\n", "流域 2050828780.0: 面积 20608.28 km², 总像元 30, 有效像元 30, 有效比例 100.00%\n", "流域 2050816870.0: 面积 50775.18 km², 总像元 80, 有效像元 80, 有效比例 100.00%\n", "流域 2050816390.0: 面积 8089.24 km², 总像元 12, 有效像元 12, 有效比例 100.00%\n", "流域 2050816320.0: 面积 26429.73 km², 总像元 41, 有效像元 41, 有效比例 100.00%\n", "流域 2050817690.0: 面积 18060.65 km², 总像元 25, 有效像元 25, 有效比例 100.00%\n", "流域 2050784800.0: 面积 4346.09 km², 总像元 7, 有效像元 7, 有效比例 100.00%\n", "流域 2050784690.0: 面积 22709.01 km², 总像元 36, 有效像元 36, 有效比例 100.00%\n", "流域 2050785900.0: 面积 3061.24 km², 总像元 6, 有效像元 6, 有效比例 100.00%\n", "流域 2050785890.0: 面积 70034.51 km², 总像元 107, 有效像元 107, 有效比例 100.00%\n", "流域 2050773460.0: 面积 27202.41 km², 总像元 43, 有效像元 43, 有效比例 100.00%\n", "流域 2050773470.0: 面积 33050.45 km², 总像元 53, 有效像元 53, 有效比例 100.00%\n", "流域 2050770120.0: 面积 12093.47 km², 总像元 20, 有效像元 20, 有效比例 100.00%\n", "流域 2050770040.0: 面积 32899.05 km², 总像元 52, 有效像元 52, 有效比例 100.00%\n", "流域 2050759640.0: 面积 76932.26 km², 总像元 119, 有效像元 119, 有效比例 100.00%\n", "流域 2050759680.0: 面积 17640.50 km², 总像元 30, 有效像元 30, 有效比例 100.00%\n", "流域 2050737850.0: 面积 83108.68 km², 总像元 133, 有效像元 133, 有效比例 100.00%\n", "流域 2050668200.0: 面积 40482.42 km², 总像元 65, 有效像元 65, 有效比例 100.00%\n", "流域 2050668190.0: 面积 22754.83 km², 总像元 38, 有效像元 38, 有效比例 100.00%\n", "流域 2050085500.0: 面积 43065.49 km², 总像元 74, 有效像元 74, 有效比例 100.00%\n", "流域 2050671620.0: 面积 11436.16 km², 总像元 19, 有效像元 19, 有效比例 100.00%\n", "流域 2050669810.0: 面积 17053.81 km², 总像元 27, 有效像元 27, 有效比例 100.00%\n", "流域 2050672240.0: 面积 138151.68 km², 总像元 224, 有效像元 224, 有效比例 100.00%\n", "流域 2050672290.0: 面积 14378.62 km², 总像元 23, 有效像元 23, 有效比例 100.00%\n", "流域 2050724600.0: 面积 54698.58 km², 总像元 89, 有效像元 89, 有效比例 100.00%\n", "流域 2050724400.0: 面积 16878.66 km², 总像元 26, 有效像元 26, 有效比例 100.00%\n", "流域 2050807910.0: 面积 62171.65 km², 总像元 95, 有效像元 95, 有效比例 100.00%\n", "流域 2050085570.0: 面积 93581.85 km², 总像元 151, 有效像元 151, 有效比例 100.00%\n", "流域 2050085590.0: 面积 92602.40 km², 总像元 143, 有效像元 143, 有效比例 100.00%\n", "流域 2050085600.0: 面积 133581.31 km², 总像元 261, 有效像元 261, 有效比例 100.00%\n", "流域 2050085610.0: 面积 156601.39 km², 总像元 265, 有效像元 265, 有效比例 100.00%\n", "流域 2050085620.0: 面积 71859.06 km², 总像元 110, 有效像元 110, 有效比例 100.00%\n", "流域 2050085690.0: 面积 52316.49 km², 总像元 86, 有效像元 86, 有效比例 100.00%\n", "流域 2050085700.0: 面积 87156.55 km², 总像元 133, 有效像元 133, 有效比例 100.00%\n", "流域 2050085720.0: 面积 43369.94 km², 总像元 66, 有效像元 66, 有效比例 100.00%\n", "流域 2050085730.0: 面积 98875.75 km², 总像元 146, 有效像元 146, 有效比例 100.00%\n", "流域 2050085740.0: 面积 37613.76 km², 总像元 58, 有效像元 58, 有效比例 100.00%\n", "流域 2050085780.0: 面积 33674.55 km², 总像元 54, 有效像元 54, 有效比例 100.00%\n", "流域 2050085830.0: 面积 40289.06 km², 总像元 61, 有效像元 61, 有效比例 100.00%\n", "流域 2050085860.0: 面积 40131.22 km², 总像元 63, 有效像元 63, 有效比例 100.00%\n", "流域 2050085870.0: 面积 25972.33 km², 总像元 40, 有效像元 40, 有效比例 100.00%\n", "流域 2050085880.0: 面积 24360.40 km², 总像元 42, 有效像元 42, 有效比例 100.00%\n", "流域 2050085980.0: 面积 18047.02 km², 总像元 28, 有效像元 28, 有效比例 100.00%\n", "流域 2050085990.0: 面积 17355.11 km², 总像元 29, 有效像元 29, 有效比例 100.00%\n", "流域 2050086000.0: 面积 25463.51 km², 总像元 49, 有效像元 49, 有效比例 100.00%\n", "流域 2050086070.0: 面积 22237.15 km², 总像元 35, 有效像元 35, 有效比例 100.00%\n", "流域 2050086110.0: 面积 18601.23 km², 总像元 30, 有效像元 30, 有效比例 100.00%\n", "流域 2050086120.0: 面积 13717.02 km², 总像元 20, 有效像元 20, 有效比例 100.00%\n", "流域 2050086180.0: 面积 24989.83 km², 总像元 49, 有效像元 49, 有效比例 100.00%\n", "流域 2050086340.0: 面积 13294.15 km², 总像元 21, 有效像元 21, 有效比例 100.00%\n", "流域 2050086720.0: 面积 11868.96 km², 总像元 20, 有效像元 20, 有效比例 100.00%\n", "流域 2050086730.0: 面积 10432.07 km², 总像元 16, 有效像元 16, 有效比例 100.00%\n", "流域 2050089360.0: 面积 2560.75 km², 总像元 4, 有效像元 4, 有效比例 100.00%\n", "流域 4050629690.0: 面积 995.02 km², 总像元 3, 有效像元 3, 有效比例 100.00%\n", "流域 4050629590.0: 面积 4558.13 km², 总像元 7, 有效像元 7, 有效比例 100.00%\n", "流域 4050631880.0: 面积 45513.57 km², 总像元 69, 有效像元 69, 有效比例 100.00%\n", "流域 4050631960.0: 面积 28490.57 km², 总像元 43, 有效像元 43, 有效比例 100.00%\n", "流域 4050625230.0: 面积 10737.71 km², 总像元 18, 有效像元 18, 有效比例 100.00%\n", "流域 4050625490.0: 面积 12671.66 km², 总像元 19, 有效像元 19, 有效比例 100.00%\n", "流域 4050534090.0: 面积 19667.91 km², 总像元 30, 有效像元 30, 有效比例 100.00%\n", "流域 4050534180.0: 面积 14506.04 km², 总像元 26, 有效像元 26, 有效比例 100.00%\n", "流域 4050563950.0: 面积 15002.41 km², 总像元 24, 有效像元 24, 有效比例 100.00%\n", "流域 4050564070.0: 面积 10705.30 km², 总像元 15, 有效像元 15, 有效比例 100.00%\n", "流域 4050578830.0: 面积 1247.11 km², 总像元 2, 有效像元 2, 有效比例 100.00%\n", "流域 4050578640.0: 面积 62627.38 km², 总像元 101, 有效像元 101, 有效比例 100.00%\n", "流域 4050585650.0: 面积 25485.80 km², 总像元 43, 有效像元 43, 有效比例 100.00%\n", "流域 4050585760.0: 面积 541.31 km², 总像元 1, 有效像元 1, 有效比例 100.00%\n", "流域 4050589340.0: 面积 31683.60 km², 总像元 50, 有效像元 50, 有效比例 100.00%\n", "流域 4050589480.0: 面积 7215.02 km², 总像元 10, 有效像元 10, 有效比例 100.00%\n", "流域 4050603550.0: 面积 6554.42 km², 总像元 10, 有效像元 10, 有效比例 100.00%\n", "流域 4050646500.0: 面积 78529.90 km², 总像元 123, 有效像元 123, 有效比例 100.00%\n", "流域 4050779600.0: 面积 159534.86 km², 总像元 243, 有效像元 243, 有效比例 100.00%\n", "流域 4050872820.0: 面积 270590.77 km², 总像元 414, 有效像元 414, 有效比例 100.00%\n", "流域 4050872680.0: 面积 128474.00 km², 总像元 189, 有效像元 189, 有效比例 100.00%\n", "流域 4050730280.0: 面积 16941.87 km², 总像元 25, 有效像元 25, 有效比例 100.00%\n", "流域 4050705180.0: 面积 19154.70 km², 总像元 29, 有效像元 29, 有效比例 100.00%\n", "流域 4050705050.0: 面积 12907.29 km², 总像元 20, 有效像元 20, 有效比例 100.00%\n", "流域 4050718400.0: 面积 29308.53 km², 总像元 47, 有效像元 47, 有效比例 100.00%\n", "流域 4050718180.0: 面积 13969.66 km², 总像元 21, 有效像元 21, 有效比例 100.00%\n", "流域 4050911800.0: 面积 89451.37 km², 总像元 133, 有效像元 133, 有效比例 100.00%\n", "流域 4050911950.0: 面积 167029.66 km², 总像元 240, 有效像元 240, 有效比例 100.00%\n", "流域 4050901850.0: 面积 132243.58 km², 总像元 197, 有效像元 197, 有效比例 100.00%\n", "流域 4050901960.0: 面积 82406.20 km², 总像元 123, 有效像元 123, 有效比例 100.00%\n", "流域 4050911460.0: 面积 94064.25 km², 总像元 138, 有效像元 138, 有效比例 100.00%\n", "流域 4050911450.0: 面积 131069.70 km², 总像元 188, 有效像元 188, 有效比例 100.00%\n", "流域 4050876680.0: 面积 79708.05 km², 总像元 117, 有效像元 117, 有效比例 100.00%\n", "流域 4050786650.0: 面积 54649.56 km², 总像元 77, 有效像元 77, 有效比例 100.00%\n", "流域 4050786460.0: 面积 32700.61 km², 总像元 50, 有效像元 50, 有效比例 100.00%\n", "流域 4050785160.0: 面积 51869.62 km², 总像元 74, 有效像元 74, 有效比例 100.00%\n", "流域 4050784890.0: 面积 20302.01 km², 总像元 33, 有效像元 33, 有效比例 100.00%\n", "流域 4050026610.0: 面积 29851.23 km², 总像元 43, 有效像元 43, 有效比例 100.00%\n", "流域 4050026660.0: 面积 37872.18 km², 总像元 55, 有效像元 55, 有效比例 100.00%\n", "流域 4050802250.0: 面积 22196.36 km², 总像元 30, 有效像元 30, 有效比例 100.00%\n", "流域 4050785020.0: 面积 20094.12 km², 总像元 28, 有效像元 28, 有效比例 100.00%\n", "流域 4050785260.0: 面积 38068.46 km², 总像元 57, 有效像元 57, 有效比例 100.00%\n", "流域 4050033640.0: 面积 17131.13 km², 总像元 24, 有效像元 24, 有效比例 100.00%\n", "流域 4050879050.0: 面积 33083.81 km², 总像元 52, 有效像元 52, 有效比例 100.00%\n", "流域 4050879040.0: 面积 97930.01 km², 总像元 138, 有效像元 138, 有效比例 100.00%\n", "流域 4050794590.0: 面积 7140.89 km², 总像元 12, 有效像元 12, 有效比例 100.00%\n", "流域 4050794730.0: 面积 1110.89 km², 总像元 2, 有效像元 2, 有效比例 100.00%\n", "流域 4050786190.0: 面积 162109.77 km², 总像元 243, 有效像元 243, 有效比例 100.00%\n", "流域 4050784370.0: 面积 26811.12 km², 总像元 43, 有效像元 43, 有效比例 100.00%\n", "流域 4050784150.0: 面积 14413.87 km², 总像元 21, 有效像元 21, 有效比例 100.00%\n", "流域 4050673650.0: 面积 11250.09 km², 总像元 18, 有效像元 18, 有效比例 100.00%\n", "流域 4050673550.0: 面积 12079.11 km², 总像元 21, 有效像元 21, 有效比例 100.00%\n", "流域 4050729890.0: 面积 25600.00 km², 总像元 40, 有效像元 40, 有效比例 100.00%\n", "流域 4050729700.0: 面积 17194.93 km², 总像元 27, 有效像元 27, 有效比例 100.00%\n", "流域 4050648310.0: 面积 92778.41 km², 总像元 146, 有效像元 146, 有效比例 100.00%\n", "流域 4050648320.0: 面积 69242.13 km², 总像元 112, 有效像元 112, 有效比例 100.00%\n", "流域 4050709990.0: 面积 31057.23 km², 总像元 46, 有效像元 46, 有效比例 100.00%\n", "流域 4050607260.0: 面积 66616.41 km², 总像元 103, 有效像元 103, 有效比例 100.00%\n", "流域 4050607420.0: 面积 72893.43 km², 总像元 111, 有效像元 111, 有效比例 100.00%\n", "流域 4050033650.0: 面积 35475.71 km², 总像元 54, 有效像元 54, 有效比例 100.00%\n", "流域 4050050210.0: 面积 82752.01 km², 总像元 140, 有效像元 140, 有效比例 100.00%\n", "流域 4050449990.0: 面积 21899.02 km², 总像元 37, 有效像元 37, 有效比例 100.00%\n", "流域 4050449660.0: 面积 91031.05 km², 总像元 159, 有效像元 159, 有效比例 100.00%\n", "流域 4050476110.0: 面积 22789.29 km², 总像元 38, 有效像元 38, 有效比例 100.00%\n", "流域 4050475170.0: 面积 53828.43 km², 总像元 86, 有效像元 86, 有效比例 100.00%\n", "流域 4050475000.0: 面积 3330.85 km², 总像元 7, 有效像元 7, 有效比例 100.00%\n", "流域 4050452670.0: 面积 27898.34 km², 总像元 47, 有效像元 47, 有效比例 100.00%\n", "流域 4050416010.0: 面积 40450.40 km², 总像元 69, 有效像元 69, 有效比例 100.00%\n", "流域 4050416430.0: 面积 6422.97 km², 总像元 13, 有效像元 13, 有效比例 100.00%\n", "流域 4050416190.0: 面积 33184.08 km², 总像元 54, 有效像元 54, 有效比例 100.00%\n", "流域 4050427020.0: 面积 9153.18 km², 总像元 13, 有效像元 13, 有效比例 100.00%\n", "流域 4050442120.0: 面积 86539.61 km², 总像元 148, 有效像元 148, 有效比例 100.00%\n", "流域 4050440300.0: 面积 40735.52 km², 总像元 66, 有效像元 66, 有效比例 100.00%\n", "流域 4050438030.0: 面积 98798.85 km², 总像元 170, 有效像元 170, 有效比例 100.00%\n", "流域 4050462690.0: 面积 76949.22 km², 总像元 129, 有效像元 129, 有效比例 100.00%\n", "流域 4050469570.0: 面积 23123.60 km², 总像元 38, 有效像元 38, 有效比例 100.00%\n", "流域 4050469680.0: 面积 28084.64 km², 总像元 44, 有效像元 44, 有效比例 100.00%\n", "流域 4050505600.0: 面积 7017.40 km², 总像元 11, 有效像元 11, 有效比例 100.00%\n", "流域 4050505750.0: 面积 1079.66 km², 总像元 3, 有效像元 3, 有效比例 100.00%\n", "流域 4050508980.0: 面积 4925.59 km², 总像元 9, 有效像元 9, 有效比例 100.00%\n", "流域 4050510750.0: 面积 2102.36 km², 总像元 4, 有效像元 4, 有效比例 100.00%\n", "流域 4050510850.0: 面积 12725.04 km², 总像元 20, 有效像元 20, 有效比例 100.00%\n", "流域 4050515470.0: 面积 24629.40 km², 总像元 39, 有效像元 39, 有效比例 100.00%\n", "流域 4050515600.0: 面积 18680.75 km², 总像元 30, 有效像元 30, 有效比例 100.00%\n", "流域 4050492120.0: 面积 10710.75 km², 总像元 17, 有效像元 17, 有效比例 100.00%\n", "流域 4050492110.0: 面积 7731.02 km², 总像元 13, 有效像元 13, 有效比例 100.00%\n", "流域 4050523580.0: 面积 11578.51 km², 总像元 20, 有效像元 20, 有效比例 100.00%\n", "流域 4050523690.0: 面积 29652.67 km², 总像元 49, 有效像元 49, 有效比例 100.00%\n", "流域 4050050220.0: 面积 64805.89 km², 总像元 108, 有效像元 108, 有效比例 100.00%\n", "流域 4050273960.0: 面积 45526.91 km², 总像元 84, 有效像元 84, 有效比例 100.00%\n", "流域 4050274280.0: 面积 62970.70 km², 总像元 109, 有效像元 109, 有效比例 100.00%\n", "流域 4050484700.0: 面积 69668.33 km², 总像元 115, 有效像元 115, 有效比例 100.00%\n", "流域 4050496820.0: 面积 39598.84 km², 总像元 62, 有效像元 62, 有效比例 100.00%\n", "流域 4050496830.0: 面积 18907.62 km², 总像元 33, 有效像元 33, 有效比例 100.00%\n", "流域 4050531200.0: 面积 9017.02 km², 总像元 14, 有效像元 14, 有效比例 100.00%\n", "流域 4050531330.0: 面积 24395.30 km², 总像元 40, 有效像元 40, 有效比例 100.00%\n", "流域 4050538040.0: 面积 38378.75 km², 总像元 63, 有效像元 63, 有效比例 100.00%\n", "流域 4050538240.0: 面积 14579.75 km², 总像元 22, 有效像元 22, 有效比例 100.00%\n", "流域 4050549780.0: 面积 37843.29 km², 总像元 57, 有效像元 57, 有效比例 100.00%\n", "流域 4050545690.0: 面积 4352.28 km², 总像元 9, 有效像元 9, 有效比例 100.00%\n", "流域 4050544080.0: 面积 20976.88 km², 总像元 34, 有效像元 34, 有效比例 100.00%\n", "流域 4050540630.0: 面积 23095.93 km², 总像元 36, 有效像元 36, 有效比例 100.00%\n", "流域 4050520710.0: 面积 31425.47 km², 总像元 51, 有效像元 51, 有效比例 100.00%\n", "流域 4050508830.0: 面积 19821.76 km², 总像元 32, 有效像元 32, 有效比例 100.00%\n", "流域 4050219380.0: 面积 18254.15 km², 总像元 30, 有效像元 30, 有效比例 100.00%\n", "流域 4050298550.0: 面积 9766.90 km², 总像元 17, 有效像元 17, 有效比例 100.00%\n", "流域 4050298640.0: 面积 12947.45 km², 总像元 24, 有效像元 24, 有效比例 100.00%\n", "流域 4050317010.0: 面积 14547.24 km², 总像元 25, 有效像元 25, 有效比例 100.00%\n", "流域 4050317280.0: 面积 6447.28 km², 总像元 10, 有效像元 10, 有效比例 100.00%\n", "流域 4050313680.0: 面积 29361.19 km², 总像元 53, 有效像元 53, 有效比例 100.00%\n", "流域 4050050240.0: 面积 17601.85 km², 总像元 33, 有效像元 33, 有效比例 100.00%\n", "流域 4050237450.0: 面积 70019.11 km², 总像元 129, 有效像元 129, 有效比例 100.00%\n", "流域 4050237460.0: 面积 14821.60 km², 总像元 26, 有效像元 26, 有效比例 100.00%\n", "流域 4050355290.0: 面积 14411.48 km², 总像元 25, 有效像元 25, 有效比例 100.00%\n", "流域 4050411980.0: 面积 31508.04 km², 总像元 53, 有效像元 53, 有效比例 100.00%\n", "流域 4050411990.0: 面积 78750.22 km², 总像元 137, 有效像元 137, 有效比例 100.00%\n", "流域 4050421200.0: 面积 28664.18 km², 总像元 49, 有效像元 49, 有效比例 100.00%\n", "流域 4050420980.0: 面积 60918.94 km², 总像元 104, 有效像元 104, 有效比例 100.00%\n", "流域 4050050270.0: 面积 110283.75 km², 总像元 177, 有效像元 177, 有效比例 100.00%\n", "流域 4050050280.0: 面积 91566.31 km², 总像元 159, 有效像元 159, 有效比例 100.00%\n", "流域 4050050290.0: 面积 87773.65 km², 总像元 161, 有效像元 161, 有效比例 100.00%\n", "流域 4050050300.0: 面积 75515.04 km², 总像元 140, 有效像元 140, 有效比例 100.00%\n", "流域 4050050310.0: 面积 73330.30 km², 总像元 143, 有效像元 143, 有效比例 100.00%\n", "流域 4050050320.0: 面积 71417.52 km², 总像元 121, 有效像元 121, 有效比例 100.00%\n", "流域 4050050340.0: 面积 53149.59 km², 总像元 95, 有效像元 95, 有效比例 100.00%\n", "流域 4050050350.0: 面积 45445.05 km², 总像元 80, 有效像元 80, 有效比例 100.00%\n", "流域 4050050370.0: 面积 98769.25 km², 总像元 170, 有效像元 170, 有效比例 100.00%\n", "流域 4050050410.0: 面积 54847.78 km², 总像元 97, 有效像元 97, 有效比例 100.00%\n", "流域 4050050420.0: 面积 65811.95 km², 总像元 106, 有效像元 106, 有效比例 100.00%\n", "流域 4050050480.0: 面积 77398.06 km², 总像元 130, 有效像元 130, 有效比例 100.00%\n", "流域 4050050500.0: 面积 76343.15 km², 总像元 124, 有效像元 124, 有效比例 100.00%\n", "流域 4050050550.0: 面积 23002.85 km², 总像元 39, 有效像元 39, 有效比例 100.00%\n", "流域 4050050560.0: 面积 89756.42 km², 总像元 154, 有效像元 154, 有效比例 100.00%\n", "流域 4050050570.0: 面积 21966.14 km², 总像元 37, 有效像元 37, 有效比例 100.00%\n", "流域 4050050580.0: 面积 44384.77 km², 总像元 76, 有效像元 76, 有效比例 100.00%\n", "流域 4050050590.0: 面积 86201.35 km², 总像元 161, 有效像元 161, 有效比例 100.00%\n", "流域 4050050600.0: 面积 48300.67 km², 总像元 78, 有效像元 78, 有效比例 100.00%\n", "流域 4050050610.0: 面积 64264.54 km², 总像元 96, 有效像元 96, 有效比例 100.00%\n", "流域 4050050700.0: 面积 19334.37 km², 总像元 33, 有效像元 33, 有效比例 100.00%\n", "流域 4050050720.0: 面积 26650.03 km², 总像元 44, 有效像元 44, 有效比例 100.00%\n", "流域 4050050730.0: 面积 56646.62 km², 总像元 106, 有效像元 106, 有效比例 100.00%\n", "流域 4050050740.0: 面积 33679.84 km², 总像元 60, 有效像元 60, 有效比例 100.00%\n", "流域 4050050810.0: 面积 73075.66 km², 总像元 113, 有效像元 113, 有效比例 100.00%\n", "流域 4050050850.0: 面积 21751.80 km², 总像元 34, 有效像元 34, 有效比例 100.00%\n", "流域 4050050910.0: 面积 30306.56 km², 总像元 45, 有效像元 45, 有效比例 100.00%\n", "流域 4050050960.0: 面积 17596.43 km², 总像元 31, 有效像元 31, 有效比例 100.00%\n", "流域 4050051010.0: 面积 17772.39 km², 总像元 33, 有效像元 33, 有效比例 100.00%\n", "流域 4050051190.0: 面积 20884.47 km², 总像元 37, 有效像元 37, 有效比例 100.00%\n", "流域 4050051400.0: 面积 26706.83 km², 总像元 42, 有效像元 42, 有效比例 100.00%\n", "流域 4050051420.0: 面积 13365.90 km², 总像元 24, 有效像元 24, 有效比例 100.00%\n", "流域 4050051440.0: 面积 20079.56 km², 总像元 39, 有效像元 39, 有效比例 100.00%\n", "流域 4050051460.0: 面积 10857.46 km², 总像元 18, 有效像元 18, 有效比例 100.00%\n", "流域 4050051540.0: 面积 31489.76 km², 总像元 57, 有效像元 57, 有效比例 100.00%\n", "流域 4050051910.0: 面积 4577.10 km², 总像元 7, 有效像元 7, 有效比例 100.00%\n", "流域 4050052210.0: 面积 6431.58 km², 总像元 15, 有效像元 15, 有效比例 100.00%\n", "流域 4050052570.0: 面积 4581.22 km², 总像元 8, 有效像元 8, 有效比例 100.00%\n", "流域 4050052690.0: 面积 3416.95 km², 总像元 6, 有效像元 6, 有效比例 100.00%\n", "流域 4050053370.0: 面积 2386.38 km², 总像元 3, 有效像元 3, 有效比例 100.00%\n", "\n", "完成294个流域的像元和面积统计\n"]}], "source": ["# 分析每个流域的有效像元数量和面积\n", "print(\"分析每个流域的有效像元数量和面积...\")\n", "\n", "# 使用等面积投影坐标系计算流域面积\n", "area_crs = 'ESRI:54009'  # Mollweide投影\n", "basins_area = basins.to_crs(area_crs)\n", "\n", "# 为每个流域创建掩膜并统计有效像元\n", "basin_pixel_stats = []\n", "\n", "# 打开栅格文件以获取完整的地理信息\n", "with rasterio.open(raster_path) as src:\n", "    for idx, basin in basins.iterrows():\n", "        basin_id = basin['HYBAS_ID']\n", "        basin_geom = basin.geometry\n", "        \n", "        # 计算流域面积（使用投影坐标系）\n", "        basin_geom_area = basins_area.iloc[idx].geometry\n", "        if not basin_geom_area.is_valid:\n", "            basin_geom_area = basin_geom_area.buffer(0)\n", "        basin_area_km2 = basin_geom_area.area / 1e6  # 转换为平方公里\n", "        \n", "        try:\n", "            # 使用流域几何体裁剪栅格\n", "            masked_data, masked_transform = mask(src, [basin_geom], crop=True, nodata=np.nan)\n", "            masked_data = masked_data[0]  # 取第一个波段\n", "            \n", "            # 统计有效像元（>=0）\n", "            valid_mask = masked_data >= 0\n", "            valid_pixels_count = np.sum(valid_mask)\n", "            total_pixels_count = np.sum(~np.isnan(masked_data))\n", "            \n", "            basin_pixel_stats.append({\n", "                'Basin_ID': basin_id,\n", "                'Basin_Area_km2': basin_area_km2,\n", "                'Total_Pixels': total_pixels_count,\n", "                'Valid_Pixels': valid_pixels_count,\n", "                'Valid_Ratio': valid_pixels_count / total_pixels_count if total_pixels_count > 0 else 0\n", "            })\n", "            \n", "            print(f\"流域 {basin_id}: 面积 {basin_area_km2:.2f} km², \"\n", "                  f\"总像元 {total_pixels_count}, 有效像元 {valid_pixels_count}, \"\n", "                  f\"有效比例 {valid_pixels_count/total_pixels_count:.2%}\" if total_pixels_count > 0 else f\"流域 {basin_id}: 面积 {basin_area_km2:.2f} km², 无像元\")\n", "            \n", "        except Exception as e:\n", "            print(f\"处理流域 {basin_id} 时出错: {e}\")\n", "            basin_pixel_stats.append({\n", "                'Basin_ID': basin_id,\n", "                'Basin_Area_km2': basin_area_km2,\n", "                'Total_Pixels': 0,\n", "                'Valid_Pixels': 0,\n", "                'Valid_Ratio': 0\n", "            })\n", "\n", "print(f\"\\n完成{len(basin_pixel_stats)}个流域的像元和面积统计\")"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["流域像元和面积统计结果已保存：Z:\\yuan\\paper3_new02\\shp\\basin_Country\\basin_pixel_statistics.csv\n", "\n", "=== 流域像元和面积统计摘要 ===\n", "流域总数：294\n", "总流域面积：11601873.78 km²\n", "平均流域面积：39462.16 km²\n", "总有效像元数：19229\n", "平均每个流域有效像元数：65\n", "\n", "前5个有效像元最多的流域：\n", "         Basin_ID  Basin_Area_km2  Valid_Pixels  Valid_Ratio\n", "155  4.050873e+09   270590.770413           414          1.0\n", "113  2.050086e+09   156601.392954           265          1.0\n", "112  2.050086e+09   133581.314845           261          1.0\n", "183  4.050786e+09   162109.774611           243          1.0\n", "154  4.050780e+09   159534.859862           243          1.0\n", "\n", "前5个面积最大的流域：\n", "         Basin_ID  Basin_Area_km2  Valid_Pixels  Valid_Ratio\n", "155  4.050873e+09   270590.770413           414          1.0\n", "163  4.050912e+09   167029.662834           240          1.0\n", "183  4.050786e+09   162109.774611           243          1.0\n", "154  4.050780e+09   159534.859862           243          1.0\n", "113  2.050086e+09   156601.392954           265          1.0\n"]}], "source": ["# 保存流域像元和面积统计结果\n", "basin_stats_df = pd.DataFrame(basin_pixel_stats)\n", "basin_stats_df = basin_stats_df.sort_values('Valid_Pixels', ascending=False)\n", "\n", "basin_stats_path = os.path.join(output_dir, \"basin_pixel_statistics.csv\")\n", "basin_stats_df.to_csv(basin_stats_path, index=False, encoding='utf-8-sig')\n", "print(f\"流域像元和面积统计结果已保存：{basin_stats_path}\")\n", "\n", "# 显示统计摘要\n", "print(\"\\n=== 流域像元和面积统计摘要 ===\")\n", "print(f\"流域总数：{len(basin_stats_df)}\")\n", "print(f\"总流域面积：{basin_stats_df['Basin_Area_km2'].sum():.2f} km²\")\n", "print(f\"平均流域面积：{basin_stats_df['Basin_Area_km2'].mean():.2f} km²\")\n", "print(f\"总有效像元数：{basin_stats_df['Valid_Pixels'].sum()}\")\n", "print(f\"平均每个流域有效像元数：{basin_stats_df['Valid_Pixels'].mean():.0f}\")\n", "print(\"\\n前5个有效像元最多的流域：\")\n", "print(basin_stats_df[['Basin_ID', 'Basin_Area_km2', 'Valid_Pixels', 'Valid_Ratio']].head())\n", "print(\"\\n前5个面积最大的流域：\")\n", "basin_stats_by_area = basin_stats_df.sort_values('Basin_Area_km2', ascending=False)\n", "print(basin_stats_by_area[['Basin_ID', 'Basin_Area_km2', 'Valid_Pixels', 'Valid_Ratio']].head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 第五步：生成流域ID栅格"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["生成流域ID栅格...\n", "原始栅格中有效像元数量：253297\n", "流域 2050465610.0: 分配了 101 个有效像元\n", "流域 2050085680.0: 分配了 83 个有效像元\n", "流域 4050007850.0: 分配了 122 个有效像元\n", "流域 4050680810.0: 分配了 95 个有效像元\n", "流域 4050214510.0: 分配了 35 个有效像元\n", "流域 4050219180.0: 分配了 64 个有效像元\n", "流域 2050000010.0: 分配了 136 个有效像元\n", "流域 2050001320.0: 分配了 119 个有效像元\n", "流域 2050002110.0: 分配了 225 个有效像元\n", "流域 2050003440.0: 分配了 8 个有效像元\n", "流域 2050003550.0: 分配了 105 个有效像元\n", "流域 2050003560.0: 分配了 50 个有效像元\n", "流域 2050004130.0: 分配了 140 个有效像元\n", "流域 2050004280.0: 分配了 60 个有效像元\n", "流域 2050005120.0: 分配了 36 个有效像元\n", "流域 2050491980.0: 分配了 110 个有效像元\n", "流域 2050491970.0: 分配了 198 个有效像元\n", "流域 2050439800.0: 分配了 70 个有效像元\n", "流域 2050006600.0: 分配了 143 个有效像元\n", "流域 2050007930.0: 分配了 87 个有效像元\n", "流域 2050469830.0: 分配了 45 个有效像元\n", "流域 2050008000.0: 分配了 128 个有效像元\n", "流域 2050008160.0: 分配了 10 个有效像元\n", "流域 2050008170.0: 分配了 14 个有效像元\n", "流域 2050008350.0: 分配了 143 个有效像元\n", "流域 2050008440.0: 分配了 10 个有效像元\n", "流域 2050008450.0: 分配了 2 个有效像元\n", "流域 2050008490.0: 分配了 80 个有效像元\n", "流域 2050543160.0: 分配了 7 个有效像元\n", "流域 2050557340.0: 分配了 25 个有效像元\n", "流域 2050557390.0: 分配了 28 个有效像元\n", "流域 2050569550.0: 分配了 48 个有效像元\n", "流域 2050575490.0: 分配了 100 个有效像元\n", "流域 2050557720.0: 分配了 24 个有效像元\n", "流域 2050557800.0: 分配了 66 个有效像元\n", "流域 2050555600.0: 分配了 3 个有效像元\n", "流域 2050548500.0: 分配了 26 个有效像元\n", "流域 2050539930.0: 分配了 78 个有效像元\n", "流域 2050540100.0: 分配了 75 个有效像元\n", "流域 2050524800.0: 分配了 14 个有效像元\n", "流域 2050525040.0: 分配了 56 个有效像元\n", "流域 2050514740.0: 分配了 50 个有效像元\n", "流域 2050514730.0: 分配了 27 个有效像元\n", "流域 2050483240.0: 分配了 24 个有效像元\n", "流域 2050488080.0: 分配了 4 个有效像元\n", "流域 2050487990.0: 分配了 37 个有效像元\n", "流域 2050488360.0: 分配了 32 个有效像元\n", "流域 2050488190.0: 分配了 1 个有效像元\n", "流域 2050477000.0: 分配了 56 个有效像元\n", "流域 2050476910.0: 分配了 53 个有效像元\n", "流域 2050008500.0: 分配了 51 个有效像元\n", "流域 2050009230.0: 分配了 11 个有效像元\n", "流域 2050009480.0: 分配了 89 个有效像元\n", "流域 2050009490.0: 分配了 23 个有效像元\n", "流域 2050009730.0: 分配了 29 个有效像元\n", "流域 2050009740.0: 分配了 12 个有效像元\n", "流域 2050009900.0: 分配了 40 个有效像元\n", "流域 2050009910.0: 分配了 179 个有效像元\n", "流域 2050012730.0: 分配了 24 个有效像元\n", "流域 2050012960.0: 分配了 8 个有效像元\n", "流域 2050012980.0: 分配了 29 个有效像元\n", "流域 2050013010.0: 分配了 132 个有效像元\n", "流域 2050013020.0: 分配了 3 个有效像元\n", "流域 2050013100.0: 分配了 11 个有效像元\n", "流域 2050013110.0: 分配了 93 个有效像元\n", "流域 2050014550.0: 分配了 149 个有效像元\n", "流域 2050065840.0: 分配了 3 个有效像元\n", "流域 2050065960.0: 分配了 18 个有效像元\n", "流域 2050065970.0: 分配了 3 个有效像元\n", "流域 2050066030.0: 分配了 58 个有效像元\n", "流域 2050066040.0: 分配了 37 个有效像元\n", "流域 2050067570.0: 分配了 139 个有效像元\n", "流域 2050067650.0: 分配了 147 个有效像元\n", "流域 2050067740.0: 分配了 101 个有效像元\n", "流域 2050067910.0: 分配了 18 个有效像元\n", "流域 2050067920.0: 分配了 18 个有效像元\n", "流域 2050068170.0: 分配了 10 个有效像元\n", "流域 2050068340.0: 分配了 17 个有效像元\n", "流域 2050068680.0: 分配了 98 个有效像元\n", "流域 2050421450.0: 分配了 24 个有效像元\n", "流域 2050643590.0: 分配了 165 个有效像元\n", "流域 2050070510.0: 分配了 91 个有效像元\n", "流域 2050070520.0: 分配了 49 个有效像元\n", "流域 2050828790.0: 分配了 109 个有效像元\n", "流域 2050828780.0: 分配了 30 个有效像元\n", "流域 2050816870.0: 分配了 80 个有效像元\n", "流域 2050816390.0: 分配了 12 个有效像元\n", "流域 2050816320.0: 分配了 41 个有效像元\n", "流域 2050817690.0: 分配了 25 个有效像元\n", "流域 2050784800.0: 分配了 7 个有效像元\n", "流域 2050784690.0: 分配了 36 个有效像元\n", "流域 2050785900.0: 分配了 6 个有效像元\n", "流域 2050785890.0: 分配了 107 个有效像元\n", "流域 2050773460.0: 分配了 43 个有效像元\n", "流域 2050773470.0: 分配了 53 个有效像元\n", "流域 2050770120.0: 分配了 20 个有效像元\n", "流域 2050770040.0: 分配了 52 个有效像元\n", "流域 2050759640.0: 分配了 119 个有效像元\n", "流域 2050759680.0: 分配了 30 个有效像元\n", "流域 2050737850.0: 分配了 133 个有效像元\n", "流域 2050668200.0: 分配了 65 个有效像元\n", "流域 2050668190.0: 分配了 38 个有效像元\n", "流域 2050085500.0: 分配了 74 个有效像元\n", "流域 2050671620.0: 分配了 19 个有效像元\n", "流域 2050669810.0: 分配了 27 个有效像元\n", "流域 2050672240.0: 分配了 224 个有效像元\n", "流域 2050672290.0: 分配了 23 个有效像元\n", "流域 2050724600.0: 分配了 89 个有效像元\n", "流域 2050724400.0: 分配了 26 个有效像元\n", "流域 2050807910.0: 分配了 95 个有效像元\n", "流域 2050085570.0: 分配了 151 个有效像元\n", "流域 2050085590.0: 分配了 143 个有效像元\n", "流域 2050085600.0: 分配了 261 个有效像元\n", "流域 2050085610.0: 分配了 265 个有效像元\n", "流域 2050085620.0: 分配了 110 个有效像元\n", "流域 2050085690.0: 分配了 86 个有效像元\n", "流域 2050085700.0: 分配了 133 个有效像元\n", "流域 2050085720.0: 分配了 66 个有效像元\n", "流域 2050085730.0: 分配了 146 个有效像元\n", "流域 2050085740.0: 分配了 58 个有效像元\n", "流域 2050085780.0: 分配了 54 个有效像元\n", "流域 2050085830.0: 分配了 61 个有效像元\n", "流域 2050085860.0: 分配了 63 个有效像元\n", "流域 2050085870.0: 分配了 40 个有效像元\n", "流域 2050085880.0: 分配了 42 个有效像元\n", "流域 2050085980.0: 分配了 28 个有效像元\n", "流域 2050085990.0: 分配了 29 个有效像元\n", "流域 2050086000.0: 分配了 49 个有效像元\n", "流域 2050086070.0: 分配了 35 个有效像元\n", "流域 2050086110.0: 分配了 30 个有效像元\n", "流域 2050086120.0: 分配了 20 个有效像元\n", "流域 2050086180.0: 分配了 49 个有效像元\n", "流域 2050086340.0: 分配了 21 个有效像元\n", "流域 2050086720.0: 分配了 20 个有效像元\n", "流域 2050086730.0: 分配了 16 个有效像元\n", "流域 2050089360.0: 分配了 4 个有效像元\n", "流域 4050629690.0: 分配了 3 个有效像元\n", "流域 4050629590.0: 分配了 7 个有效像元\n", "流域 4050631880.0: 分配了 69 个有效像元\n", "流域 4050631960.0: 分配了 43 个有效像元\n", "流域 4050625230.0: 分配了 18 个有效像元\n", "流域 4050625490.0: 分配了 19 个有效像元\n", "流域 4050534090.0: 分配了 30 个有效像元\n", "流域 4050534180.0: 分配了 26 个有效像元\n", "流域 4050563950.0: 分配了 24 个有效像元\n", "流域 4050564070.0: 分配了 15 个有效像元\n", "流域 4050578830.0: 分配了 2 个有效像元\n", "流域 4050578640.0: 分配了 101 个有效像元\n", "流域 4050585650.0: 分配了 43 个有效像元\n", "流域 4050585760.0: 分配了 1 个有效像元\n", "流域 4050589340.0: 分配了 50 个有效像元\n", "流域 4050589480.0: 分配了 10 个有效像元\n", "流域 4050603550.0: 分配了 10 个有效像元\n", "流域 4050646500.0: 分配了 123 个有效像元\n", "流域 4050779600.0: 分配了 243 个有效像元\n", "流域 4050872820.0: 分配了 414 个有效像元\n", "流域 4050872680.0: 分配了 189 个有效像元\n", "流域 4050730280.0: 分配了 25 个有效像元\n", "流域 4050705180.0: 分配了 29 个有效像元\n", "流域 4050705050.0: 分配了 20 个有效像元\n", "流域 4050718400.0: 分配了 47 个有效像元\n", "流域 4050718180.0: 分配了 21 个有效像元\n", "流域 4050911800.0: 分配了 133 个有效像元\n", "流域 4050911950.0: 分配了 240 个有效像元\n", "流域 4050901850.0: 分配了 197 个有效像元\n", "流域 4050901960.0: 分配了 123 个有效像元\n", "流域 4050911460.0: 分配了 138 个有效像元\n", "流域 4050911450.0: 分配了 188 个有效像元\n", "流域 4050876680.0: 分配了 117 个有效像元\n", "流域 4050786650.0: 分配了 77 个有效像元\n", "流域 4050786460.0: 分配了 50 个有效像元\n", "流域 4050785160.0: 分配了 74 个有效像元\n", "流域 4050784890.0: 分配了 33 个有效像元\n", "流域 4050026610.0: 分配了 43 个有效像元\n", "流域 4050026660.0: 分配了 55 个有效像元\n", "流域 4050802250.0: 分配了 30 个有效像元\n", "流域 4050785020.0: 分配了 28 个有效像元\n", "流域 4050785260.0: 分配了 57 个有效像元\n", "流域 4050033640.0: 分配了 24 个有效像元\n", "流域 4050879050.0: 分配了 52 个有效像元\n", "流域 4050879040.0: 分配了 138 个有效像元\n", "流域 4050794590.0: 分配了 12 个有效像元\n", "流域 4050794730.0: 分配了 2 个有效像元\n", "流域 4050786190.0: 分配了 243 个有效像元\n", "流域 4050784370.0: 分配了 43 个有效像元\n", "流域 4050784150.0: 分配了 21 个有效像元\n", "流域 4050673650.0: 分配了 18 个有效像元\n", "流域 4050673550.0: 分配了 21 个有效像元\n", "流域 4050729890.0: 分配了 40 个有效像元\n", "流域 4050729700.0: 分配了 27 个有效像元\n", "流域 4050648310.0: 分配了 146 个有效像元\n", "流域 4050648320.0: 分配了 112 个有效像元\n", "流域 4050709990.0: 分配了 46 个有效像元\n", "流域 4050607260.0: 分配了 103 个有效像元\n", "流域 4050607420.0: 分配了 111 个有效像元\n", "流域 4050033650.0: 分配了 54 个有效像元\n", "流域 4050050210.0: 分配了 140 个有效像元\n", "流域 4050449990.0: 分配了 37 个有效像元\n", "流域 4050449660.0: 分配了 159 个有效像元\n", "流域 4050476110.0: 分配了 38 个有效像元\n", "流域 4050475170.0: 分配了 86 个有效像元\n", "流域 4050475000.0: 分配了 7 个有效像元\n", "流域 4050452670.0: 分配了 47 个有效像元\n", "流域 4050416010.0: 分配了 69 个有效像元\n", "流域 4050416430.0: 分配了 13 个有效像元\n", "流域 4050416190.0: 分配了 54 个有效像元\n", "流域 4050427020.0: 分配了 13 个有效像元\n", "流域 4050442120.0: 分配了 148 个有效像元\n", "流域 4050440300.0: 分配了 66 个有效像元\n", "流域 4050438030.0: 分配了 170 个有效像元\n", "流域 4050462690.0: 分配了 129 个有效像元\n", "流域 4050469570.0: 分配了 38 个有效像元\n", "流域 4050469680.0: 分配了 44 个有效像元\n", "流域 4050505600.0: 分配了 11 个有效像元\n", "流域 4050505750.0: 分配了 3 个有效像元\n", "流域 4050508980.0: 分配了 9 个有效像元\n", "流域 4050510750.0: 分配了 4 个有效像元\n", "流域 4050510850.0: 分配了 20 个有效像元\n", "流域 4050515470.0: 分配了 39 个有效像元\n", "流域 4050515600.0: 分配了 30 个有效像元\n", "流域 4050492120.0: 分配了 17 个有效像元\n", "流域 4050492110.0: 分配了 13 个有效像元\n", "流域 4050523580.0: 分配了 20 个有效像元\n", "流域 4050523690.0: 分配了 49 个有效像元\n", "流域 4050050220.0: 分配了 108 个有效像元\n", "流域 4050273960.0: 分配了 84 个有效像元\n", "流域 4050274280.0: 分配了 109 个有效像元\n", "流域 4050484700.0: 分配了 115 个有效像元\n", "流域 4050496820.0: 分配了 62 个有效像元\n", "流域 4050496830.0: 分配了 33 个有效像元\n", "流域 4050531200.0: 分配了 14 个有效像元\n", "流域 4050531330.0: 分配了 40 个有效像元\n", "流域 4050538040.0: 分配了 63 个有效像元\n", "流域 4050538240.0: 分配了 22 个有效像元\n", "流域 4050549780.0: 分配了 57 个有效像元\n", "流域 4050545690.0: 分配了 9 个有效像元\n", "流域 4050544080.0: 分配了 34 个有效像元\n", "流域 4050540630.0: 分配了 36 个有效像元\n", "流域 4050520710.0: 分配了 51 个有效像元\n", "流域 4050508830.0: 分配了 32 个有效像元\n", "流域 4050219380.0: 分配了 30 个有效像元\n", "流域 4050298550.0: 分配了 17 个有效像元\n", "流域 4050298640.0: 分配了 24 个有效像元\n", "流域 4050317010.0: 分配了 25 个有效像元\n", "流域 4050317280.0: 分配了 10 个有效像元\n", "流域 4050313680.0: 分配了 53 个有效像元\n", "流域 4050050240.0: 分配了 33 个有效像元\n", "流域 4050237450.0: 分配了 129 个有效像元\n", "流域 4050237460.0: 分配了 26 个有效像元\n", "流域 4050355290.0: 分配了 25 个有效像元\n", "流域 4050411980.0: 分配了 53 个有效像元\n", "流域 4050411990.0: 分配了 137 个有效像元\n", "流域 4050421200.0: 分配了 49 个有效像元\n", "流域 4050420980.0: 分配了 104 个有效像元\n", "流域 4050050270.0: 分配了 177 个有效像元\n", "流域 4050050280.0: 分配了 159 个有效像元\n", "流域 4050050290.0: 分配了 161 个有效像元\n", "流域 4050050300.0: 分配了 140 个有效像元\n", "流域 4050050310.0: 分配了 143 个有效像元\n", "流域 4050050320.0: 分配了 121 个有效像元\n", "流域 4050050340.0: 分配了 95 个有效像元\n", "流域 4050050350.0: 分配了 80 个有效像元\n", "流域 4050050370.0: 分配了 170 个有效像元\n", "流域 4050050410.0: 分配了 97 个有效像元\n", "流域 4050050420.0: 分配了 106 个有效像元\n", "流域 4050050480.0: 分配了 130 个有效像元\n", "流域 4050050500.0: 分配了 124 个有效像元\n", "流域 4050050550.0: 分配了 39 个有效像元\n", "流域 4050050560.0: 分配了 154 个有效像元\n", "流域 4050050570.0: 分配了 37 个有效像元\n", "流域 4050050580.0: 分配了 76 个有效像元\n", "流域 4050050590.0: 分配了 161 个有效像元\n", "流域 4050050600.0: 分配了 78 个有效像元\n", "流域 4050050610.0: 分配了 96 个有效像元\n", "流域 4050050700.0: 分配了 33 个有效像元\n", "流域 4050050720.0: 分配了 44 个有效像元\n", "流域 4050050730.0: 分配了 106 个有效像元\n", "流域 4050050740.0: 分配了 60 个有效像元\n", "流域 4050050810.0: 分配了 113 个有效像元\n", "流域 4050050850.0: 分配了 34 个有效像元\n", "流域 4050050910.0: 分配了 45 个有效像元\n", "流域 4050050960.0: 分配了 31 个有效像元\n", "流域 4050051010.0: 分配了 33 个有效像元\n", "流域 4050051190.0: 分配了 37 个有效像元\n", "流域 4050051400.0: 分配了 42 个有效像元\n", "流域 4050051420.0: 分配了 24 个有效像元\n", "流域 4050051440.0: 分配了 39 个有效像元\n", "流域 4050051460.0: 分配了 18 个有效像元\n", "流域 4050051540.0: 分配了 57 个有效像元\n", "流域 4050051910.0: 分配了 7 个有效像元\n", "流域 4050052210.0: 分配了 15 个有效像元\n", "流域 4050052570.0: 分配了 8 个有效像元\n", "流域 4050052690.0: 分配了 6 个有效像元\n", "流域 4050053370.0: 分配了 3 个有效像元\n", "\n", "总共处理了 19229 个有效像元\n", "流域ID栅格已保存：Z:\\yuan\\paper3_new02\\shp\\basin_Country\\basin_id_raster.tif\n", "栅格中包含的流域ID数量：192\n", "流域ID范围：2050000000 - 4050912000\n"]}], "source": ["# 生成流域ID栅格（只在有效像元位置赋值流域ID）\n", "print(\"生成流域ID栅格...\")\n", "\n", "with rasterio.open(raster_path) as src:\n", "    # 创建输出栅格，初始化为nodata\n", "    basin_id_raster = np.full(src.shape, src.nodata, dtype=np.float32)\n", "    \n", "    # 读取原始栅格数据\n", "    original_data = src.read(1)\n", "    \n", "    # 只处理有效像元（>=0）\n", "    valid_mask = original_data >= 0\n", "    \n", "    print(f\"原始栅格中有效像元数量：{np.sum(valid_mask)}\")\n", "    \n", "    # 为每个流域分配ID\n", "    processed_pixels = 0\n", "    \n", "    for idx, basin in basins.iterrows():\n", "        basin_id = basin['HYBAS_ID']\n", "        basin_geom = basin.geometry\n", "        \n", "        try:\n", "            # 创建流域掩膜\n", "            basin_mask = rasterize(\n", "                [basin_geom],\n", "                out_shape=src.shape,\n", "                transform=src.transform,\n", "                fill=0,\n", "                default_value=1,\n", "                dtype=np.uint8\n", "            )\n", "            \n", "            # 找到同时满足有效像元和流域范围的像元\n", "            basin_valid_mask = valid_mask & (basin_mask == 1)\n", "            basin_pixel_count = np.sum(basin_valid_mask)\n", "            \n", "            if basin_pixel_count > 0:\n", "                # 在这些位置赋值流域ID\n", "                basin_id_raster[basin_valid_mask] = basin_id\n", "                processed_pixels += basin_pixel_count\n", "                print(f\"流域 {basin_id}: 分配了 {basin_pixel_count} 个有效像元\")\n", "            \n", "        except Exception as e:\n", "            print(f\"处理流域 {basin_id} 时出错: {e}\")\n", "    \n", "    print(f\"\\n总共处理了 {processed_pixels} 个有效像元\")\n", "    \n", "    # 保存流域ID栅格\n", "    basin_id_raster_path = os.path.join(output_dir, \"basin_id_raster.tif\")\n", "    \n", "    # 更新profile\n", "    profile = src.profile.copy()\n", "    profile.update({\n", "        'dtype': rasterio.float32,\n", "        'nodata': -9999,\n", "        'compress': 'lzw'\n", "    })\n", "    \n", "    # 将nodata值设置为-9999\n", "    basin_id_raster[basin_id_raster == src.nodata] = -9999\n", "    \n", "    with rasterio.open(basin_id_raster_path, 'w', **profile) as dst:\n", "        dst.write(basin_id_raster, 1)\n", "    \n", "    print(f\"流域ID栅格已保存：{basin_id_raster_path}\")\n", "    \n", "    # 统计结果\n", "    unique_ids = np.unique(basin_id_raster[basin_id_raster != -9999])\n", "    print(f\"栅格中包含的流域ID数量：{len(unique_ids)}\")\n", "    print(f\"流域ID范围：{unique_ids.min():.0f} - {unique_ids.max():.0f}\")"]}], "metadata": {"kernelspec": {"display_name": "gee", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}