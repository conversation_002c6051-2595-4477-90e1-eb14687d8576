import os
import numpy as np
import rasterio
from pathlib import Path
import h5py  # 添加h5py导入
import geopandas as gpd
import pandas as pd  # 添加pandas导入
import concurrent.futures


def process_year(year, mask_data, profile, output_dir, n, m, mat_dir, pre_yz):
    mat_path = os.path.join(mat_dir, f"Pre_{year}.mat")
    print(f"\n=== 处理{year}年数据 ===")
    with h5py.File(mat_path, 'r') as mat_data:
        pre_data = np.array(mat_data['Pre_year'])
        pre_data = pre_data.T

    # 重塑数据
    pre_data_reshaped = pre_data.reshape(n, m, -1)
    pre_data_reshaped = np.transpose(pre_data_reshaped, (1, 0, 2))  # (m, n, days)

    valid_mask = (mask_data >= 0)
    valid_idx = np.where(valid_mask.flatten())[0]
    valid_count = len(valid_idx)

    # 拉平有效像元数据 (valid_count, days)
    data_all = pre_data_reshaped[valid_mask, :]
    # 只取有效像元的阈值
    pre_yz_valid = pre_yz[valid_mask.flatten(), :]

    # 计算10个指标
    outdata = compute_precip_indices(data_all, pre_yz_valid)  # (valid_count, 10)

    # 还原为(m, n)并保存
    index_names = ['R90pD','R90pTOT','R95pD','R95pTOT','R99pD','R99pTOT','R90pI','R95pI','R99pI','RX1-day']
    for idx, name in enumerate(index_names):
        out_arr = np.full((m, n), np.nan, dtype=np.float32)
        out_arr[valid_mask] = outdata[:, idx]
        # === 新增：为每个指标创建单独的文件夹 ===
        index_dir = os.path.join(output_dir, name)
        os.makedirs(index_dir, exist_ok=True)
        out_tif = os.path.join(index_dir, f"{name}_{year}.tif")
        profile.update(dtype=rasterio.float32, count=1, compress='lzw')
        with rasterio.open(out_tif, 'w', **profile) as dst:
            dst.write(out_arr, 1)
        print(f"已保存{year}年{name}到: {out_tif}")


def read_thresholds(threshold_paths, m, n):
    """
    读取极端降水阈值tif，返回(m*n, 3)的数组
    threshold_paths: [90阈值tif, 95阈值tif, 99阈值tif]
    """
    pre_yz = np.zeros((m * n, 3), dtype=np.float32)
    for i, path in enumerate(threshold_paths):
        with rasterio.open(path) as src:
            data = src.read(1)
            pre_yz[:, i] = data.reshape(m * n)
    return pre_yz


def compute_precip_indices(data_all, pre_yz):
    """
    data_all: (n, days)  每个像元全年数据
    pre_yz: (n, 3)       每个像元的90/95/99阈值
    返回: (n, 10)         10个指标
    """
    n, days = data_all.shape
    outdata = np.full((n, 10), np.nan, dtype=np.float32)
    for i in range(n):
        row = data_all[i, :]
        yz90, yz95, yz99 = pre_yz[i, :]
        # R90pD, R90pTOT
        if np.isnan(yz90):
            R90pD = np.nan
            R90pTOT = np.nan
        else:
            mask90 = row > yz90
            R90pD = np.sum(mask90)
            R90pTOT = np.sum(row[mask90])
        # R95pD, R95pTOT
        if np.isnan(yz95):
            R95pD = np.nan
            R95pTOT = np.nan
        else:
            mask95 = row > yz95
            R95pD = np.sum(mask95)
            R95pTOT = np.sum(row[mask95])
        # R99pD, R99pTOT
        if np.isnan(yz99):
            R99pD = np.nan
            R99pTOT = np.nan
        else:
            mask99 = row > yz99
            R99pD = np.sum(mask99)
            R99pTOT = np.sum(row[mask99])
        # RX1-day
        RX1_day = np.nanmax(row)
        # 组合
        outdata[i, 0] = R90pD
        outdata[i, 1] = R90pTOT
        outdata[i, 2] = R95pD
        outdata[i, 3] = R95pTOT
        outdata[i, 4] = R99pD
        outdata[i, 5] = R99pTOT
        outdata[i, 6] = R90pTOT / R90pD if R90pD > 0 else np.nan
        outdata[i, 7] = R95pTOT / R95pD if R95pD > 0 else np.nan
        outdata[i, 8] = R99pTOT / R99pD if R99pD > 0 else np.nan
        outdata[i, 9] = RX1_day
    return outdata


if __name__ == '__main__':
    # 路径集中定义
    mask_path = "/mnt/sdb2/yuanshuai/wanpan/yuan/ERA5-Land/fbl_025/EPEs/yz_600_1440/pre_yz_90_1971_2020.tif"
    mat_dir = "/mnt/sdb2/yuanshuai/wanpan/yuan/ERA5-Land/fbl_025/mat/Pre/day"
    output_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/EPEs/historical'

    # 读取掩膜文件
    with rasterio.open(mask_path) as src:
        mask_data = src.read(1)
        mask_rows = mask_data.shape[0]
        m, n = mask_data.shape
        profile = src.profile  # 获取空间参考等信息

    # 输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 定义年份范围
    start_year = 1971
    end_year = 2020
    years = list(range(start_year, end_year + 1))
    # 读取阈值
    threshold_paths = [
        '/mnt/sdb2/yuanshuai/wanpan/yuan/ERA5-Land/fbl_025/EPEs/yz_600_1440/pre_yz_90_1971_2020.tif',
        '/mnt/sdb2/yuanshuai/wanpan/yuan/ERA5-Land/fbl_025/EPEs/yz_600_1440/pre_yz_95_1971_2020.tif',
        '/mnt/sdb2/yuanshuai/wanpan/yuan/ERA5-Land/fbl_025/EPEs/yz_600_1440/pre_yz_99_1971_2020.tif'
    ]
    pre_yz = read_thresholds(threshold_paths, m, n)

    args = (mask_data, profile, output_dir, n, m, mat_dir, pre_yz)
    with concurrent.futures.ProcessPoolExecutor(max_workers=25) as executor:
        futures = [executor.submit(process_year, year, *args) for year in years]
        for f in concurrent.futures.as_completed(futures):
            f.result()