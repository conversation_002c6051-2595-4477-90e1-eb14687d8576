import os
import numpy as np
import rasterio
from pathlib import Path
import concurrent.futures

def process_year_mme(year, mask_data, profile, output_dir, n, m, ssp, models, base_dir):
    print(f"\n=== 处理{year}年MME数据 ===")
    
    # 指标名称
    index_names = ['R90pD','R90pTOT','R95pD','R95pTOT','R99pD','R99pTOT','R90pI','R95pI','R99pI','RX1-day']
    
    for idx, name in enumerate(index_names):
        print(f"处理指标: {name}")
        
        # 收集所有模式的数据
        all_models_data = []
        valid_models = []
        
        for model in models:
            # 构建每个模式的输入文件路径
            model_file = os.path.join(base_dir, ssp, model, name, f"{name}_{year}.tif")
            
            if os.path.exists(model_file):
                try:
                    with rasterio.open(model_file) as src:
                        data = src.read(1)
                        all_models_data.append(data)
                        valid_models.append(model)
                except Exception as e:
                    print(f"读取{model}的{name}_{year}.tif失败: {e}")
                    continue
            else:
                print(f"文件不存在: {model_file}")
        
        if not all_models_data:
            print(f"警告: {year}年{name}指标没有找到任何有效数据")
            continue
        
        # 转换为numpy数组
        all_models_data = np.array(all_models_data)
        
        # 计算MME平均值
        mme_data = np.nanmean(all_models_data, axis=0)
        
        # 保存MME结果
        index_dir = os.path.join(output_dir, name)
        os.makedirs(index_dir, exist_ok=True)
        out_tif = os.path.join(index_dir, f"{name}_{year}.tif")
        
        profile.update(dtype=rasterio.float32, count=1, compress='lzw')
        with rasterio.open(out_tif, 'w', **profile) as dst:
            dst.write(mme_data.astype(np.float32), 1)
        
        print(f"已保存{year}年{name} MME结果，使用{len(valid_models)}个模式: {', '.join(valid_models)}")
        print(f"保存路径: {out_tif}")

if __name__ == '__main__':
    # 掩膜路径
    mask_path = "/mnt/sdb2/yuanshuai/wanpan/yuan/ERA5-Land/fbl_025/EPEs/yz_600_1440/pre_yz_90_1971_2020.tif"
    
    # 模型列表
    models = [
        'ACCESS-CM2', 'ACCESS-ESM1-5', 'BCC-CSM2-MR', 'CanESM5', 'CMCC-ESM2', 
        'CNRM-CM6-1', 'CNRM-ESM2-1', 'EC-Earth3', 'EC-Earth3-Veg-LR', 'FGOALS-g3', 
        'GFDL-ESM4', 'GISS-E2-1-G', 'INM-CM4-8', 'INM-CM5-0', 'IPSL-CM6A-LR', 
        'MIROC6', 'MIROC-ES2L', 'MPI-ESM1-2-HR', 'MPI-ESM1-2-LR', 'MRI-ESM2-0',
        'NorESM2-LM', 'NorESM2-MM', 'TaiESM1'
    ]
    
    # 情景列表
    ssps = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
    
    # 读取掩膜获取地理信息
    with rasterio.open(mask_path) as src:
        mask_data = src.read(1)
        m, n = mask_data.shape
        profile = src.profile
    
    # 年份范围
    start_year = 2021
    end_year = 2100
    years = list(range(start_year, end_year + 1))
    
    # 基础目录（第四步的输出目录）
    base_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/EPEs/ssp_models_jz'
    
    for ssp in ssps:
        print(f"\n=== 处理情景 {ssp} 的MME ===")
        
        # MME输出目录
        mme_output_dir = f'/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new02/EPEs/ssp_mme_jz/{ssp}'
        os.makedirs(mme_output_dir, exist_ok=True)
        
        # 多进程处理年份
        with concurrent.futures.ProcessPoolExecutor(max_workers=40) as executor:
            futures = []
            for year in years:
                futures.append(executor.submit(
                    process_year_mme, year, mask_data, profile, mme_output_dir, 
                    n, m, ssp, models, base_dir
                ))
            
            for f in concurrent.futures.as_completed(futures):
                f.result()
        
        print(f"完成情景 {ssp} 的MME计算")
    
    print("\n=== 所有MME计算完成 ===")
