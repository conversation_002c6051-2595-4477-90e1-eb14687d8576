# EPE数据掩膜处理脚本使用说明

## 概述
本套脚本用于对极端降水事件(EPE)数据进行掩膜处理，根据研究区掩膜栅格对数据2-8进行裁剪，保存到指定路径下。

## 文件说明

### 1. 核心处理脚本
- **EPE_data_masking.py**: 主要的数据处理类，包含掩膜处理逻辑
- **process_all_epe_data.py**: 批量处理所有数据类型的主脚本
- **test_single_data_type.py**: 测试单个数据类型处理的脚本
- **verify_processing_results.py**: 验证处理结果的脚本

### 2. 数据路径配置
- **输入数据路径**: `Z:/yuan/paper3_new02/EPEs/`
- **掩膜文件**: `Z:/yuan/paper3_new02/shp/basin_Country_res/basin_025_1.tif`
- **输出路径**: `Z:/yuan/paper3_new02/EPEs_clip/`

### 3. 处理的数据类型
1. **historical**: ERA5-Land历史极端降水指标数据 (1971-2020年)
2. **historical_models**: CMIP6历史模式极端降水指标数据 (1971-2014年)
3. **EPEs_delta_models**: 偏差校正因子数据
4. **ssp_models_jz**: 校正后未来情景极端降水数据 (2021-2100年)
5. **ssp_mme_jz**: 多模式集合结果数据 (2021-2100年)
6. **historical_mean**: 历史多年平均数据 (1971-2020年)
7. **ssp_mme_jz_5yr**: 未来逐五年平均数据 (2025-2100年)

## 环境要求

### Python包依赖
```bash
conda install rasterio numpy tqdm
```

### 系统要求
- Python 3.7+
- 足够的磁盘空间用于输出数据
- 对输入和输出路径的读写权限

## 使用方法

### 方法1: 批量处理所有数据 (推荐)
```bash
python process_all_epe_data.py
```
这个脚本会：
- 检查运行前提条件
- 按顺序处理所有7种数据类型
- 生成详细的处理日志
- 显示处理进度和耗时统计

### 方法2: 单独测试某个数据类型
```bash
python test_single_data_type.py historical
```
可用的数据类型参数：
- `historical`
- `historical_models`
- `EPEs_delta_models`
- `ssp_models_jz`
- `ssp_mme_jz`
- `historical_mean`
- `ssp_mme_jz_5yr`

### 方法3: 验证处理结果
```bash
python verify_processing_results.py
```
这个脚本会：
- 检查所有输出文件的完整性
- 验证文件格式和数据形状
- 生成详细的验证报告

## 处理逻辑

### 掩膜处理步骤
1. 加载研究区掩膜栅格 (有效像元值为1)
2. 遍历输入目录中的所有.tif文件
3. 对每个文件应用掩膜:
   - 保留掩膜值为1的像元
   - 将掩膜值不为1的像元设置为nodata
4. 保持原始文件的相对路径结构
5. 将处理后的文件保存到输出目录

### 文件结构保持
输出文件会保持与输入文件相同的目录结构和文件名，例如：
```
输入: Z:/yuan/paper3_new02/EPEs/historical/R90pD/R90pD_1971.tif
输出: Z:/yuan/paper3_new02/EPEs_clip/historical/R90pD/R90pD_1971.tif
```

## 日志和监控

### 日志文件
- 处理过程会生成 `epe_processing.log` 日志文件
- 包含详细的处理信息、错误记录和统计数据

### 进度监控
- 使用tqdm显示文件处理进度条
- 实时显示当前处理的文件和完成百分比
- 显示每个数据类型的处理耗时

## 错误处理

### 常见问题及解决方案

1. **掩膜文件不存在**
   - 检查路径: `Z:/yuan/paper3_new02/shp/basin_Country_res/basin_025_1.tif`
   - 确认文件存在且可读

2. **输入数据路径不存在**
   - 检查网络驱动器Z:是否正确挂载
   - 确认数据路径是否正确

3. **权限错误**
   - 确保对输入和输出路径有读写权限
   - 检查磁盘空间是否充足

4. **数据形状不匹配**
   - 脚本会自动检查数据形状与掩膜是否匹配
   - 不匹配的文件会被跳过并记录警告

## 性能优化建议

1. **分批处理**: 如果数据量很大，可以使用单个数据类型测试脚本分批处理
2. **磁盘I/O**: 确保输入和输出路径在高速存储设备上
3. **内存管理**: 脚本会逐个处理文件，避免内存溢出

## 验证建议

处理完成后，建议：
1. 运行验证脚本检查结果完整性
2. 随机抽查几个输出文件确认掩膜效果
3. 检查输出文件的统计信息是否合理

## 联系信息
如有问题，请检查日志文件或联系开发人员。
